# Документация по Google Analytics и отслеживанию данных
## Проект TravelBusinessClass

### Оглавление
1. [Обзор системы](#обзор-системы)
2. [Архитектура отслеживания](#архитектура-отслеживания)
3. [Google Tag Manager](#google-tag-manager)
4. [Центральный модуль GoogleEvent](#центральный-модуль-googleevent)
5. [Типы отправляемых событий](#типы-отправляемых-событий)
6. [UTM отслеживание](#utm-отслеживание)
7. [Пользовательская идентификация](#пользовательская-идентификация)
8. [Структуры данных](#структуры-данных)
9. [База данных](#база-данных)
10. [GDPR соответствие](#gdpr-соответствие)
11. [Техническая реализация](#техническая-реализация)

---

## Обзор системы

Проект TravelBusinessClass использует комплексную систему аналитики для отслеживания пользовательского поведения, конверсий и маркетинговых кампаний. Система включает:

- **Google Tag Manager (GTM)** - основной инструмент для управления тегами
- **Google Analytics** - веб-аналитика через GTM
- **Enhanced Ecommerce** - отслеживание электронной коммерции
- **UTM отслеживание** - анализ источников трафика
- **Внутренняя аналитика** - собственная база данных для хранения данных

---

## Архитектура отслеживания

### Компоненты системы

```
Пользователь → Frontend (JavaScript) → dataLayer → GTM → Google Analytics
                     ↓
              Backend (PHP) → База данных → Внутренняя аналитика
```

### Основные файлы

| Файл | Назначение |
|------|------------|
| `frontend/static/app/js/lib/GoogleEvent.js` | Центральный модуль для отправки событий |
| `backend/components/UtmBehavior.php` | UTM отслеживание и сохранение |
| `backend/models/UtmVisit.php` | Модель для хранения UTM данных |
| `backend/views/layouts/main.php` | Инициализация GTM |

---

## Google Tag Manager

### Контейнеры GTM

**Основной сайт:**
- **ID контейнера:** `GTM-MQSGVHQ`
- **Расположение:** `backend/views/layouts/main.php`
- **Загрузка:** С задержкой 5 секунд

**ABT тема:**
- **ID контейнера:** `GTM-TC8JPFJ`
- **Расположение:** `backend/themes/abt/layouts/main.php`
- **Загрузка:** Немедленно

### Код инициализации

```javascript
(function(w,d,s,l,i){
    w[l]=w[l]||[];
    w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});
    var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
    j.async=true;
    j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
    f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-MQSGVHQ')
```

---

## Центральный модуль GoogleEvent

### Основная структура

```javascript
window.dataLayer = window.dataLayer || [];
const googleEvent = {
    send(data) {
        window.dataLayer.push(data)
    },
    itineraryClass: {
        premium: 'Premium Class',
        business: 'Business Class',
        first: 'First Class',
    },
    itineraryType: {
        oneWay: 'One Way',
        roundTrip: 'Round Trip',
        multi: 'Multi City',
    },
    data: {},
    fromCode: '',
    toCode: ''
}
```

### Методы

#### `send(data)`
Отправляет данные в dataLayer для GTM

#### `sendDimensions(product)`
Отправляет пользовательские измерения:
- `dimension1`: Код аэропорта назначения
- `dimension2`: Тип страницы ('Offer Detail')
- `dimension3`: Цена продукта
- `dimension4`: Код аэропорта отправления

---

## Типы отправляемых событий

### 1. Enhanced Ecommerce Events

#### Product Detail View
Отправляется при просмотре деталей продукта:

```javascript
{
    event: "gtm-ee-event",
    gtmUaEventCategory: "Enhanced Ecommerce",
    gtmUaEventAction: "Product Detail",
    gtmUaEventNonInteraction: "True",
    ecommerce: {
        detail: {
            actionField: {
                list: "Premium Class" // или Business/First
            },
            products: [{
                name: "JFK - LHR",
                id: "JFK2LHR",
                price: 6000.66,
                category: "Round Trip",
                list: "Premium Class",
                position: 0
            }]
        }
    }
}
```

#### Add to Cart
Отправляется при добавлении в корзину:

```javascript
{
    event: "gtm-ee-event",
    gtmUaEventCategory: "Enhanced Ecommerce",
    gtmUaEventAction: "Add To Cart",
    gtmUaEventNonInteraction: "True",
    ecommerce: {
        add: {
            actionField: {
                list: "Business Class"
            },
            products: [{
                name: "JFK - LHR",
                id: "JFK2LHR",
                price: 9200.54,
                category: "One Way",
                list: "Business Class",
                position: 1,
                quantity: 1
            }]
        }
    }
}
```

#### Product Click
Отправляется при клике на продукт:

```javascript
{
    event: "gtm-ee-event",
    gtmUaEventCategory: "Enhanced Ecommerce",
    gtmUaEventAction: "Product Click",
    gtmUaEventNonInteraction: "True",
    ecommerce: {
        click: {
            actionField: {
                list: "First Class"
            },
            products: [{
                name: "JFK - LHR",
                id: "JFK2LHR",
                price: 11000.83,
                category: "Multi City",
                list: "First Class",
                position: 2
            }]
        }
    }
}
```

### 2. Product Impressions
Отправляется при показе списка продуктов:

```javascript
{
    event: "gtm-ee-event",
    gtmUaEventCategory: "Enhanced Ecommerce",
    gtmUaEventAction: "Product Impressions",
    gtmUaEventNonInteraction: "True",
    ecommerce: {
        impressions: [
            // массив всех продуктов
        ]
    }
}
```

---

## UTM отслеживание

### Компонент UtmBehavior

Автоматически отслеживает и сохраняет UTM параметры для каждого посещения.

#### Отслеживаемые параметры

| Параметр | Описание | Источник |
|----------|----------|----------|
| `utm_source` | Источник трафика | URL параметр |
| `utm_medium` | Канал трафика | URL параметр |
| `utm_campaign` | Название кампании | URL параметр |
| `utm_content` | Содержание объявления | URL параметр |
| `utm_term` | Ключевое слово | URL параметр |
| `gclid` | Google Ads Click ID | URL параметр |
| `gbraid` | Google Ads Enhanced Conversions | URL параметр |
| `gad_source` | Google Ads Source | URL параметр |

#### Автоматическое определение Google Ads

```php
if ($request->getQueryParam('gad_source', false)
    || $request->getQueryParam('gbraid', false)
    || $request->getQueryParam('gclid', false)
) {
    $utm = [
        'utm_source' => 'google',
        'utm_medium' => 'cpc',
        'utm_campaign' => 'not_identified',
    ];
}
```

### Сохранение UTM данных

Данные сохраняются в таблицу `utm_visits` со следующей информацией:
- UTM параметры
- Google Analytics Client ID (`_ga` cookie)
- Device ID (`did` cookie)
- IP адрес
- User Agent
- Referer
- Текущий URL
- Время создания

---

## Пользовательская идентификация

### Используемые cookies

#### `_ga` (Google Analytics Client ID)
- **Источник:** Google Analytics
- **Назначение:** Уникальная идентификация пользователя в GA
- **Формат:** `GA1.2.XXXXXXXXX.XXXXXXXXX`

#### `did` (Device ID)
- **Источник:** nginx userid модуль
- **Назначение:** Внутренняя идентификация устройства
- **Срок жизни:** 365 дней
- **Домен:** `.travelbusinessclass.com`

#### `dJ` (Jivo Control)
- **Назначение:** Контроль загрузки Jivo чата
- **Поведение:** Если установлен, чат не загружается

### Настройка nginx для Device ID

```nginx
userid         on;
userid_name    did;
userid_domain  travelbusinessclass.com;
userid_path    /;
userid_expires 365d;
userid_p3p     'policyref="/w3c/p3p.xml", CP="CUR ADM OUR NOR STA NID"';
```

---

## Структуры данных

### Структура продукта для аналитики

```javascript
{
    name: "JFK - LHR",           // Маршрут
    id: "JFK2LHR",               // Уникальный ID
    price: 6000.66,              // Цена
    category: "Round Trip",       // Тип поездки
    list: "Premium Class",       // Класс обслуживания
    position: 0                  // Позиция в списке
}
```

### Данные формы бронирования

```javascript
{
    price: [
        {
            type: 'premium',
            price: 6000.66,
            oldPrice: 0
        },
        {
            type: 'business',
            price: 9200.54,
            oldPrice: 0
        },
        {
            type: 'first',
            price: 11000.83,
            oldPrice: 0
        }
    ],
    passengers: {
        ADT: 1,  // Взрослые
        CHD: 0,  // Дети
        INF: 0   // Младенцы
    },
    contact: {
        name: "",
        phone: "",
        email: "",
        sendBySms: false
    },
    itinerary: [{
        from: {},
        to: {},
        departureDate: null,
        returnDate: null
    }]
}
```

---

## База данных

### Таблица `utm_visits`

Хранит информацию о посещениях с UTM параметрами.

#### Структура таблицы

```sql
CREATE TABLE utm_visits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ga VARCHAR(255),              -- Google Analytics Client ID
    did VARCHAR(255),             -- Device ID
    utm_source VARCHAR(255),      -- Источник трафика
    utm_medium VARCHAR(255),      -- Канал трафика
    utm_campaign VARCHAR(255),    -- Название кампании
    utm_content VARCHAR(255),     -- Содержание объявления
    utm_term VARCHAR(255),        -- Ключевое слово
    referer VARCHAR(2048),        -- HTTP Referer
    url VARCHAR(2048),            -- Текущий URL
    ip VARCHAR(255),              -- IP адрес
    ua VARCHAR(255),              -- User Agent
    created_at INT NOT NULL       -- Время создания (timestamp)
);
```

#### Модель UtmVisit (PHP)

```php
/**
 * @property int $id
 * @property string|null $ga
 * @property string|null $did
 * @property string|null $utm_source
 * @property string|null $utm_medium
 * @property string|null $utm_campaign
 * @property string|null $utm_content
 * @property string|null $utm_term
 * @property string|null $referer
 * @property string|null $url
 * @property string|null $ip
 * @property string|null $ua
 * @property int $created_at
 */
class UtmVisit extends ActiveRecord
```

### Таблица `lead`

Хранит информацию о лидах (потенциальных клиентах).

#### Структура таблицы

```sql
CREATE TABLE lead (
    id INT PRIMARY KEY AUTO_INCREMENT,
    slug VARCHAR(100) NOT NULL UNIQUE,  -- Уникальный идентификатор
    data TEXT NOT NULL,                 -- JSON данные формы
    created_at INT NOT NULL,            -- Время создания
    updated_at INT NOT NULL,            -- Время обновления
    can_sync BOOLEAN DEFAULT 0,         -- Можно ли синхронизировать
    is_sync BOOLEAN DEFAULT 0,          -- Синхронизирован ли
    is_clean BOOLEAN DEFAULT 0,         -- Очищены ли данные
    ip VARCHAR(39),                     -- IP адрес
    mail_send_at INT                    -- Время отправки письма
);
```

---

## GDPR соответствие

### Автоматическое определение региона

Система автоматически определяет европейских пользователей и показывает им уведомление о согласии на обработку данных.

```javascript
// Проверка временной зоны пользователя
if (Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone.includes('Europe')) {
    this.isOpen = true;  // Показать GDPR уведомление
} else {
    this.onAccept();     // Автоматически принять для не-европейских пользователей
}
```

### Управление согласием

```javascript
onAccept() {
    localStorage.setItem('GDPR_ACCEPT', 1);
    this.isOpen = false;
}
```

### Альтернативный метод определения (устаревший)

```javascript
// Использование внешнего API для определения региона
fetch('https://ipinfo.io/json')
.then(response => response.json())
.then(data => {
    if (data.timezone.includes('Europe')) {
        this.isOpen = true;
    } else {
        this.onAccept();
    }
})
```

---

## Техническая реализация

### Инициализация данных на странице

#### Страница предложения (Offer Page)

```php
// backend/views/offer-page/view.php
window.offerData = <?= Json::encode($offerPage) ?>;
window.resultPageData = <?= Json::encode($resultPageData) ?>;
window.resultPageDataCompleteUrl = <?= Json::encode(Url::to(['/offer-page/complete'])) ?>;
window.codeMap = <?= Json::encode($destinationsByCode) ?>;

window.googleEventData = {
    name: `to <?=$offerPage->name?>`,
    id: `ALL2<?=$offerPage->name?>`,
    price: <?=$offerPage->cheapestPrice[$offerPage->class.'_price']?>
}
```

### Отслеживание переходов

#### TrackController

```php
// backend/controllers/TrackController.php
public function actionIndex($id, $url)
{
    $queueComponent = \Yii::$app->get('redisCustomQueue');

    $send = $queueComponent->sendMessage('bo',
        CustomQueueMessage::instance('trackInfo', [
            'url' => $url,
            'id' => $id,
            'ip' => \Yii::$app->getRequest()->getUserIP(),
            'utm' => UtmBehavior::getCurrentUtmMetrics()
        ])
    );

    return $this->redirect("https://{$url}");
}
```

### Кэширование UTM данных

```php
// Кэширование UTM данных на 1 час
$cacheKey = 'utm_'. md5(\json_encode($condition));

$utm_visit = Yii::$app->cache->getOrSet($cacheKey, function () use ($condition) {
    return UtmVisit::find()
        ->where(['>=', 'created_at', time()-86400]) // Последние 24 часа
        ->andWhere($condition)
        ->orderBy(['created_at' => SORT_DESC])
        ->one()?->getAttributes() ?? ['ga' => $ga, 'did' => $did];
}, 3600);
```

### Webpack конфигурация

```javascript
// frontend/static/webpack.settings.js
entry: {
    'waiting': '@/js/waiting.js',
    'result': '@/js/result-page.js',
    'deal': '@/js/best-deal.js',
    'offer': '@/js/offer-page.js',
    'review': '@/js/review.js',
    'app': [
        '@/js/index.js',
        '@/js/top-deals.js',
        '@/js/review.js',
        '@/styles/app-base.css',
        '@/styles/app-components.css',
        '@/styles/app-utilities.css',
    ],
}
```

---

## Поток данных

### 1. Пользователь заходит на сайт
- Устанавливается `did` cookie (nginx)
- Загружается GTM с задержкой 5 секунд
- Проверяются UTM параметры
- Сохраняются данные в `utm_visits`

### 2. Просмотр продукта
- Инициализируется `googleEvent.data`
- Отправляется событие "Product Detail"
- Отправляются custom dimensions

### 3. Взаимодействие с продуктом
- Клик: событие "Product Click"
- Изменение класса: новое событие "Product Detail"

### 4. Добавление в корзину
- Отправляется событие "Add To Cart"
- Создается лид в базе данных
- Перенаправление на страницу успеха

### 5. Отслеживание конверсий
- Данные отправляются в очередь Redis
- Обновляется информация о лиде
- Отправляется email подтверждение

---

## Заключение

Система аналитики TravelBusinessClass обеспечивает:

- **Полное отслеживание** пользовательского пути
- **Соответствие GDPR** для европейских пользователей
- **Детальную аналитику** Enhanced Ecommerce
- **Внутреннюю аналитику** через собственную базу данных
- **UTM отслеживание** для анализа эффективности кампаний
- **Кэширование данных** для оптимизации производительности

Все данные собираются в соответствии с политикой конфиденциальности и требованиями GDPR.
