/**
 * Composable for working with Jivo chat
 */
export function useJivo() {
    const { $jivo } = useNuxtApp()

    return {
        /**
         * Open Jivo callback button programmatically
         */
        openCallbackButton: () => {
            $jivo.openCallbackButton()
        },

        /**
         * Disable Jivo chat (set cookie)
         */
        disable: () => {
            $jivo.disable()
        },

        /**
         * Enable Jivo chat (remove cookie)
         */
        enable: () => {
            $jivo.enable()
        },

        /**
         * Check if chat is disabled
         */
        isDisabled: computed(() => {
            if (import.meta.server) { return true }

            return $jivo.isDisabled()
        }),

        /**
         * Chat loading status
         */
        isLoaded: computed(() => {
            if (import.meta.server) { return false }

            return !!document.querySelector('script[src*="jivosite.com"]')
        }),
    }
}
