import type {
    DataLayerEvent,
    AnalyticsProduct,
    CustomDimensionsEvent,
    ItineraryClass,
    ItineraryType,
    ItineraryClassMap,
    ItineraryTypeMap,
} from '~/types/analytics'

/**
 * Composable for working with Google Analytics and Google Tag Manager
 * Provides SSR-safe methods for tracking events and managing dataLayer
 */
export function useGoogleAnalytics() {
    const config = useRuntimeConfig()
    const isClient = import.meta.client

    // Constants for mapping
    const itineraryClassMap: ItineraryClassMap = {
        premium: 'Premium Class',
        business: 'Business Class',
        first: 'First Class',
    }

    const itineraryTypeMap: ItineraryTypeMap = {
        oneWay: 'One Way',
        roundTrip: 'Round Trip',
        multi: 'Multi City',
    }

    /**
     * Initialize dataLayer if not exists (client-side only)
     */
    const initializeDataLayer = () => {
        if (!isClient) { return }

        if (!window.dataLayer) {
            window.dataLayer = []
        }
    }

    /**
     * Send event to dataLayer
     */
    const sendEvent = (event: DataLayerEvent | Record<string, any>) => {
        if (!isClient) {
            if (config.public.debug) {
                console.log('[GA] Server-side event (not sent):', event)
            }

            return
        }

        initializeDataLayer()

        try {
            window.dataLayer.push(event)

            if (config.public.debug) {
                console.log('[GA] Event sent:', event)
            }
        } catch (error) {
            console.error('[GA] Error sending event:', error)
        }
    }

    /**
     * Send custom dimensions
     */
    const sendDimensions = (product: AnalyticsProduct) => {
        const dimensionsEvent: CustomDimensionsEvent = {
            event: 'custom-dimensions',
            gtmUaEventCategory: 'Custom Dimensions',
            gtmUaEventAction: 'Set Dimensions',
            gtmUaEventNonInteraction: 'True',
            dimension1: product.id.split('2')[1] || '', // Destination code
            dimension2: 'Offer Detail',
            dimension3: product.price,
            dimension4: product.id.split('2')[0] || '', // Origin code
        }

        sendEvent(dimensionsEvent)
    }

    /**
     * Get class display name
     */
    const getClassDisplayName = (classType: ItineraryClass): string => {
        return itineraryClassMap[classType]
    }

    /**
     * Get type display name
     */
    const getTypeDisplayName = (tripType: ItineraryType): string => {
        return itineraryTypeMap[tripType]
    }

    /**
     * Create product object for analytics
     */
    const createProduct = (
        name: string,
        id: string,
        price: number,
        category: ItineraryType,
        serviceClass: ItineraryClass,
        position: number = 0,
        quantity: number = 1,
    ): AnalyticsProduct => {
        return {
            name,
            id,
            price,
            category: getTypeDisplayName(category),
            list: getClassDisplayName(serviceClass),
            position,
            quantity,
        }
    }

    /**
     * Check if GTM is loaded
     */
    const isGtmLoaded = computed(() => {
        if (!isClient) { return false }

        return !!window.dataLayer && window.dataLayer.length > 0
    })

    /**
     * Wait for GTM to be loaded
     */
    const waitForGtm = async (timeout: number = 10000): Promise<boolean> => {
        if (!isClient) { return false }

        return new Promise((resolve) => {
            const startTime = Date.now()

            const checkGtm = () => {
                if (isGtmLoaded.value) {
                    resolve(true)

                    return
                }

                if (Date.now() - startTime > timeout) {
                    console.warn('[GA] GTM loading timeout')
                    resolve(false)

                    return
                }

                setTimeout(checkGtm, 100)
            }

            checkGtm()
        })
    }

    /**
     * Send event with GTM loading check
     */
    const sendEventSafe = async (event: DataLayerEvent | Record<string, any>) => {
        if (!isClient) {
            sendEvent(event)

            return
        }

        const gtmLoaded = await waitForGtm(5000)

        if (!gtmLoaded && config.public.debug) {
            console.warn('[GA] GTM not loaded, sending event anyway')
        }

        sendEvent(event)
    }

    /**
     * Get Google Analytics Client ID from _ga cookie
     */
    const getGaClientId = (): string | null => {
        if (!isClient) { return null }

        const gaCookie = document.cookie
            .split('; ')
            .find(row => row.startsWith('_ga='))

        if (!gaCookie) { return null }

        // _ga cookie format: GA1.2.XXXXXXXXX.XXXXXXXXX
        const parts = gaCookie.split('=')[1]?.split('.')

        if (parts && parts.length >= 4) {
            return `${parts[2]}.${parts[3]}`
        }

        return null
    }

    /**
     * Get Device ID from did cookie
     */
    const getDeviceId = (): string | null => {
        if (!isClient) { return null }

        const didCookie = document.cookie
            .split('; ')
            .find(row => row.startsWith('did='))

        if (!didCookie) { return null }

        return didCookie.split('=')[1] ?? null
    }

    return {
        // Core methods
        sendEvent,
        sendEventSafe,
        sendDimensions,
        initializeDataLayer,

        // Product helpers
        createProduct,
        getClassDisplayName,
        getTypeDisplayName,

        // State
        isGtmLoaded: readonly(isGtmLoaded),

        // Utilities
        waitForGtm,
        getGaClientId,
        getDeviceId,

        // Constants
        itineraryClassMap: readonly(itineraryClassMap),
        itineraryTypeMap: readonly(itineraryTypeMap),
    }
}
