import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'

//

import type { CalendarOptions, CalendarRangeValue, CalendarValue } from '~/composables/useCalendar'

export type DatepickerOptions = {
    /**
     * Types:
     * Date value - for basic mode
     * Dates array - for range mode
     */
    modelValue?: CalendarValue,

    /**
     * Date to set calendar view
     */
    viewDate?: Date,

    /**
     * Select range of 2 dates
     */
    range?: boolean,

    /**
     * Show multiple calendars that are linked to each other
     */
    multi?: boolean | number,

    /**
     * A function that will be called to resolve visible calendars dates
     * By default it uses `multi` prop to determine how many calendars to show and
     * places them around chunk of months that contains `displayDate`
     */
    resolveCalendars?: (options: {
        displayDate: Dayjs,
        navigationOffset: number,
        multi: number,
    }) => Date[],

    /**
     * Allow navigation
     */
    navigation?: boolean | {
        /** Determine how many months with be changed on navigation */
        step?: number | 'auto',
    },

    /**
     * Whenever model value is changed calendar will swipe to that date
     */
    track?: boolean,

    /**
     * Props that will be passed to calendar
     */
    calendarProps?: CalendarOptions,

    /**
     * Mobile mode
     * Hover events will be disabled
     */
    mobile?: boolean,

    /**
     * Function that will be called to determine if forward navigation is allowed
     */
    canNavigateForward?: (calendars: Date[]) => boolean,

    /**
     * Function that will be called to determine if backward navigation is allowed
     */
    canNavigateBackward?: (calendars: Date[]) => boolean,

    /**
     * Resolve offset of calendars view
     */
    calendarOffset?: (displayDate: Date) => number,

    /**
     * Show date in month view
     */
    valueMode?: 'day' | 'month',
}

export type DatepickerEmits = {
    'update:modelValue': [value: CalendarValue],
    'dateSelect': [date: Dayjs],
    'dateHover': [date: Dayjs | undefined],
}

//

export default function useDatepicker(options: DatepickerOptions, emit: <E extends keyof DatepickerEmits, P extends DatepickerEmits[E]>(event: E, ...args: P) => void) {
    if (!getCurrentScope()) {
        throw new Error('useDatepicker() must be called from the setup() of a component or within an effect scope.')
    }

    const isRangeMode = computed(() => options.range === true)

    const initialDate = options.modelValue

    const orderedModelValue = computed<CalendarValue>(() => {
        const modelValue = options.modelValue

        if (!isRangeMode.value) {
            return modelValue
        }

        if (!modelValue) {
            return
        }

        const rangeValue = Array.isArray(modelValue) ? modelValue : [modelValue]

        const value: CalendarRangeValue = [rangeValue?.[0] || undefined, rangeValue?.[1] || undefined]

        if (value[0] && value[1] && value[0] > value[1]) {
            return [value[1], value[0]]
        }

        return value
    })

    const displayDate = computed<Dayjs>(() => {
        const modelValue = options.track ? orderedModelValue.value : initialDate

        const arrayValue = Array.isArray(modelValue) ? modelValue : [modelValue]

        const value = arrayValue?.[0] || arrayValue?.[1]
        const date = value && !options.viewDate ? value : options.viewDate

        const fallbackDate = dayjs()

        // if (isRangeMode.value && Array.isArray(modelValue)) {
        //     const max = modelValue?.[1] || modelValue?.[0]
        //
        //     if (fallbackDate.add(multi.value - 1, 'month').isSame(max, 'month')) {
        //         return fallbackDate
        //     }
        // }

        return date ? dayjs(date) : fallbackDate
    })

    const multi = computed<number>(() => (options.multi === true ? 2 : options.multi || 1))

    const calendars = computed<Date[]>(() => {
        if (options.resolveCalendars) {
            return options.resolveCalendars({
                displayDate: displayDate.value,
                navigationOffset: navigationOffset.value,
                multi: multi.value,
            })
        }

        const chunkBy = multi.value

        const calendarOffset = (options.calendarOffset ? options.calendarOffset(displayDate.value.toDate()) : 0)
        const offset = navigationOffset.value + calendarOffset

        const date = displayDate.value

        const viewChunk = Math.ceil((date.month() + 1 - calendarOffset) / chunkBy)

        return [...Array(chunkBy).keys()].map((n) => {
            return date.startOf('month').set('month', (viewChunk - 1) * chunkBy + n + offset).toDate()
        })
    })

    // Navigation
    // ==========
    const navigationOffset = ref(0)

    const navigate = (value: number) => {
        navigationOffset.value += value
    }

    const navigateByStep = (value: number) => {
        const navigationOptions = options.navigation === true ? {} : options.navigation || {}

        const step = navigationOptions.step === 'auto' ? multi.value : (navigationOptions.step || multi.value)

        navigate(step * value)
    }

    const navigateForward = () => {
        if (!canNavigateForward.value) {
            return
        }

        navigateByStep(+1)
    }

    const navigateBackward = () => {
        if (!canNavigateBackward.value) {
            return
        }

        navigateByStep(-1)
    }

    const canNavigateForward = computed(() => {
        return options.canNavigateForward ? options.canNavigateForward(calendars.value) : true
    })

    const canNavigateBackward = computed(() => {
        return options.canNavigateBackward ? options.canNavigateBackward(calendars.value) : true
    })

    // Values handling
    // ===============
    const handleDateClick = (clickedDate: Dayjs) => {
        emit('dateSelect', clickedDate)

        const date = clickedDate.toDate()

        date.setHours(12, 0, 0, 0)

        if (isRangeMode.value) {
            let value: CalendarRangeValue = [undefined, undefined]

            const current = options.modelValue as CalendarRangeValue

            if (!current || (current[0] && current[1])) {
                value = [date, undefined]
            } else if (current[0] && !current[1]) {
                value = [current[0], date]
            } else if (!current[0] && current[1]) {
                value = [date, current[1]]
            }

            if (value[0] && value[1] && value[0] > value[1]) {
                value = [value[1], value[0]]
            }

            emit('update:modelValue', value)
        } else {
            emit('update:modelValue', date)
        }
    }

    const hoveredDate = ref<Date>()

    const handleDateHover = (date: Dayjs | undefined) => {
        emit('dateHover', date)

        hoveredDate.value = date?.toDate() || undefined
    }

    const handleKeysNavigation = (event: KeyboardEvent) => {
        if (!event.ctrlKey) {
            return
        }

        const stop = () => {
            event.preventDefault()
            event.stopImmediatePropagation()
        }

        if (event.code === 'ArrowRight') {
            stop()
            navigateForward()
        } else if (event.code === 'ArrowLeft') {
            stop()
            navigateBackward()
        } else if (event.code === 'ArrowUp') {
            stop()
            navigate(12)
        } else if (event.code === 'ArrowDown') {
            stop()
            navigate(-12)
        }
    }

    watch(() => options.modelValue, () => {
        if (options.track) {
            navigationOffset.value = 0
        }
    })

    return {
        isRangeMode,

        calendars,
        hoveredDate,

        navigate,
        navigateByStep,
        navigateForward,
        navigateBackward,
        canNavigateForward,
        canNavigateBackward,

        handleDateClick,
        handleDateHover,

        handleKeysNavigation,
    }
}
