import type { UtmParameters, UtmVisit } from '~/types/analytics'

/**
 * Composable for UTM parameters tracking and storage
 * Handles UTM parameter extraction, Google Ads detection, and data persistence
 */
export function useUtmTracking() {
    const route = useRoute()
    const { getGaClientId, getDeviceId } = useGoogleAnalytics()
    const config = useRuntimeConfig()

    // Reactive UTM data
    const utmData = ref<UtmParameters>({})
    const isGoogleAds = ref(false)

    /**
     * Extract UTM parameters from URL query
     */
    const extractUtmFromQuery = (query: Record<string, any>): UtmParameters => {
        return {
            utm_source: query.utm_source as string,
            utm_medium: query.utm_medium as string,
            utm_campaign: query.utm_campaign as string,
            utm_content: query.utm_content as string,
            utm_term: query.utm_term as string,
            gclid: query.gclid as string,
            gbraid: query.gbraid as string,
            gad_source: query.gad_source as string,
        }
    }

    /**
     * Detect if traffic is from Google Ads
     */
    const detectGoogleAds = (query: Record<string, any>): boolean => {
        return !!(
            query.gad_source ||
            query.gbraid ||
            query.gclid
        )
    }

    /**
     * Get default UTM parameters for Google Ads traffic
     */
    const getGoogleAdsDefaults = (): Partial<UtmParameters> => {
        return {
            utm_source: 'google',
            utm_medium: 'cpc',
            utm_campaign: 'not_identified',
        }
    }

    /**
     * Initialize UTM tracking from current route
     */
    const initializeUtmTracking = () => {
        const query = route.query
        const extractedUtm = extractUtmFromQuery(query)

        // Check if this is Google Ads traffic
        const isGoogleAdsTraffic = detectGoogleAds(query)
        isGoogleAds.value = isGoogleAdsTraffic

        if (isGoogleAdsTraffic) {
            // Merge Google Ads defaults with extracted UTM
            utmData.value = {
                ...getGoogleAdsDefaults(),
                ...extractedUtm,
            }
        } else {
            utmData.value = extractedUtm
        }

        // Remove undefined values
        Object.keys(utmData.value).forEach(key => {
            if (utmData.value[key as keyof UtmParameters] === undefined) {
                delete utmData.value[key as keyof UtmParameters]
            }
        })
    }

    /**
     * Get current UTM parameters
     */
    const getCurrentUtm = (): UtmParameters => {
        return { ...utmData.value }
    }

    /**
     * Check if any UTM parameters are present
     */
    const hasUtmParameters = computed(() => {
        return Object.keys(utmData.value).some(key =>
            utmData.value[key as keyof UtmParameters],
        )
    })

    /**
     * Create UTM visit object for storage
     */
    const createUtmVisit = (): UtmVisit => {
        const visit: UtmVisit = {
            ...getCurrentUtm(),
            ga: getGaClientId(),
            did: getDeviceId(),
            created_at: Math.floor(Date.now() / 1000),
        }

        // Add client-side data if available
        if (import.meta.client) {
            visit.referer = document.referrer || undefined
            visit.url = window.location.href
            visit.ua = navigator.userAgent
        }

        return visit
    }

    /**
     * Save UTM visit to backend (requires API endpoint)
     */
    const saveUtmVisit = async (): Promise<boolean> => {
        if (!hasUtmParameters.value) {
            return false
        }

        try {
            const visit = createUtmVisit()

            // This would need to be implemented as an API endpoint
            await $fetch('/api/utm/visit', {
                method: 'POST',
                body: visit,
            })

            if (config.public.debug) {
                console.log('[UTM] Visit saved:', visit)
            }

            return true
        } catch (error) {
            console.error('[UTM] Error saving visit:', error)

            return false
        }
    }

    /**
     * Get UTM parameters as URL query string
     */
    const getUtmQueryString = (): string => {
        const params = new URLSearchParams()

        Object.entries(utmData.value).forEach(([key, value]) => {
            if (value) {
                params.append(key, value)
            }
        })

        return params.toString()
    }

    /**
     * Append UTM parameters to URL
     */
    const appendUtmToUrl = (url: string): string => {
        const queryString = getUtmQueryString()

        if (!queryString) { return url }

        const separator = url.includes('?') ? '&' : '?'

        return `${url}${separator}${queryString}`
    }

    /**
     * Store UTM data in localStorage for persistence
     */
    const storeUtmInLocalStorage = () => {
        if (!import.meta.client || !hasUtmParameters.value) { return }

        try {
            localStorage.setItem('utm_data', JSON.stringify(utmData.value))
            localStorage.setItem('utm_timestamp', Date.now().toString())
        } catch (error) {
            console.error('[UTM] Error storing in localStorage:', error)
        }
    }

    /**
     * Load UTM data from localStorage
     */
    const loadUtmFromLocalStorage = (): boolean => {
        if (!import.meta.client) { return false }

        try {
            const storedUtm = localStorage.getItem('utm_data')
            const timestamp = localStorage.getItem('utm_timestamp')

            if (!storedUtm || !timestamp) { return false }

            // Check if data is not older than 24 hours
            const age = Date.now() - parseInt(timestamp)
            const maxAge = 24 * 60 * 60 * 1000 // 24 hours

            if (age > maxAge) {
                localStorage.removeItem('utm_data')
                localStorage.removeItem('utm_timestamp')

                return false
            }

            const parsedUtm = JSON.parse(storedUtm)
            utmData.value = { ...utmData.value, ...parsedUtm }

            return true
        } catch (error) {
            console.error('[UTM] Error loading from localStorage:', error)

            return false
        }
    }

    /**
     * Clear stored UTM data
     */
    const clearUtmData = () => {
        utmData.value = {}
        isGoogleAds.value = false

        if (import.meta.client) {
            localStorage.removeItem('utm_data')
            localStorage.removeItem('utm_timestamp')
        }
    }

    // Initialize on composable creation
    onMounted(() => {
        // First try to load from localStorage
        const hasStoredUtm = loadUtmFromLocalStorage()

        // Then initialize from current route (this will override stored data if present)
        initializeUtmTracking()

        // Store the new data
        if (hasUtmParameters.value) {
            storeUtmInLocalStorage()
            // Auto-save visit data
            nextTick(() => {
                saveUtmVisit()
            })
        }
    })

    return {
        // Reactive data
        utmData: readonly(utmData),
        isGoogleAds: readonly(isGoogleAds),
        hasUtmParameters,

        // Methods
        initializeUtmTracking,
        getCurrentUtm,
        createUtmVisit,
        saveUtmVisit,
        getUtmQueryString,
        appendUtmToUrl,
        storeUtmInLocalStorage,
        loadUtmFromLocalStorage,
        clearUtmData,

        // Utilities
        extractUtmFromQuery,
        detectGoogleAds,
        getGoogleAdsDefaults,
    }
}
