import type { GdprSettings } from '~/types/analytics'

/**
 * Composable for GDPR compliance management
 * Handles automatic region detection and consent management
 */
export function useGdprCompliance() {
    const config = useRuntimeConfig()

    // Reactive GDPR state
    const gdprSettings = ref<GdprSettings>({
        isAccepted: false,
        isEuropeanUser: false,
        showBanner: false,
    })

    const isLoading = ref(true)

    /**
     * Check if user is in European timezone using Intl API
     */
    const checkEuropeanTimezone = (): boolean => {
        if (!import.meta.client) { return false }

        try {
            const timezone = Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone

            return timezone ? timezone.includes('Europe') : false
        } catch (error) {
            console.error('[GDPR] Error checking timezone:', error)

            return false
        }
    }

    /**
     * Check if user is in European region using external API (fallback)
     */
    const checkEuropeanRegionApi = async (): Promise<boolean> => {
        if (!import.meta.client) { return false }

        try {
            const response = await fetch('https://ipinfo.io/json', {
                timeout: 5000,
            })

            if (!response.ok) { return false }

            const data = await response.json()

            return data.timezone ? data.timezone.includes('Europe') : false
        } catch (error) {
            console.error('[GDPR] Error checking region via API:', error)

            return false
        }
    }

    /**
     * Determine if user is European
     */
    const determineEuropeanUser = async (): Promise<boolean> => {
        // Primary method: timezone check
        const isEuropeanByTimezone = checkEuropeanTimezone()

        if (isEuropeanByTimezone) {
            return true
        }

        // Fallback method: API check (optional, can be disabled for privacy)
        if (config.public.enableGdprApiCheck) {
            return await checkEuropeanRegionApi()
        }

        return false
    }

    /**
     * Check if consent has been given
     */
    const checkStoredConsent = (): boolean => {
        if (!import.meta.client) { return false }

        try {
            const consent = localStorage.getItem('GDPR_ACCEPT')

            return consent === '1' || consent === 'true'
        } catch (error) {
            console.error('[GDPR] Error checking stored consent:', error)

            return false
        }
    }

    /**
     * Store consent decision
     */
    const storeConsent = (accepted: boolean) => {
        if (!import.meta.client) { return }

        try {
            if (accepted) {
                localStorage.setItem('GDPR_ACCEPT', '1')
                // Set cookie for server-side access
                document.cookie = 'gdpr_consent=1; path=/; max-age=31536000; SameSite=Lax'
            } else {
                localStorage.removeItem('GDPR_ACCEPT')
                // Remove cookie
                document.cookie = 'gdpr_consent=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
            }

            if (config.public.debug) {
                console.log('[GDPR] Consent stored:', accepted)
            }
        } catch (error) {
            console.error('[GDPR] Error storing consent:', error)
        }
    }

    /**
     * Accept GDPR consent
     */
    const acceptConsent = () => {
        gdprSettings.value.isAccepted = true
        gdprSettings.value.showBanner = false
        storeConsent(true)

        // Emit event for other components
        if (import.meta.client) {
            window.dispatchEvent(new CustomEvent('gdpr:accepted'))
        }
    }

    /**
     * Reject GDPR consent
     */
    const rejectConsent = () => {
        gdprSettings.value.isAccepted = false
        gdprSettings.value.showBanner = false
        storeConsent(false)

        // Emit event for other components
        if (import.meta.client) {
            window.dispatchEvent(new CustomEvent('gdpr:rejected'))
        }
    }

    /**
     * Initialize GDPR compliance check
     */
    const initializeGdpr = async () => {
        isLoading.value = true

        try {
            // Check if user is European
            const isEuropean = await determineEuropeanUser()
            gdprSettings.value.isEuropeanUser = isEuropean

            if (isEuropean) {
                // Check if consent was already given
                const hasConsent = checkStoredConsent()
                gdprSettings.value.isAccepted = hasConsent
                gdprSettings.value.showBanner = !hasConsent
            } else {
                // Non-European users: auto-accept
                gdprSettings.value.isAccepted = true
                gdprSettings.value.showBanner = false
                storeConsent(true)
            }

            if (config.public.debug) {
                console.log('[GDPR] Initialized:', gdprSettings.value)
            }
        } catch (error) {
            console.error('[GDPR] Error initializing:', error)
            // Default to showing banner for safety
            gdprSettings.value.showBanner = true
        } finally {
            isLoading.value = false
        }
    }

    /**
     * Reset GDPR settings (for testing)
     */
    const resetGdpr = () => {
        gdprSettings.value = {
            isAccepted: false,
            isEuropeanUser: false,
            showBanner: false,
        }

        if (import.meta.client) {
            localStorage.removeItem('GDPR_ACCEPT')
            document.cookie = 'gdpr_consent=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
        }
    }

    /**
     * Check if analytics can be loaded
     */
    const canLoadAnalytics = computed(() => {
        return gdprSettings.value.isAccepted && !isLoading.value
    })

    /**
     * Get consent status for server-side rendering
     */
    const getConsentFromCookie = (cookieString?: string): boolean => {
        if (!cookieString) { return false }

        const cookies = cookieString.split(';').map(c => c.trim())
        const gdprCookie = cookies.find(c => c.startsWith('gdpr_consent='))

        return gdprCookie ? gdprCookie.split('=')[1] === '1' : false
    }

    // Initialize on client-side mount
    onMounted(() => {
        initializeGdpr()
    })

    // Watch for consent changes to emit events
    watch(() => gdprSettings.value.isAccepted, (newValue, oldValue) => {
        if (newValue !== oldValue && import.meta.client) {
            const eventName = newValue ? 'gdpr:accepted' : 'gdpr:rejected'
            window.dispatchEvent(new CustomEvent(eventName))
        }
    })

    return {
        // Reactive state
        gdprSettings: readonly(gdprSettings),
        isLoading: readonly(isLoading),
        canLoadAnalytics,

        // Actions
        acceptConsent,
        rejectConsent,
        initializeGdpr,
        resetGdpr,

        // Utilities
        checkEuropeanTimezone,
        checkEuropeanRegionApi,
        checkStoredConsent,
        getConsentFromCookie,

        // Computed
        showBanner: computed(() => gdprSettings.value.showBanner),
        isEuropeanUser: computed(() => gdprSettings.value.isEuropeanUser),
        isAccepted: computed(() => gdprSettings.value.isAccepted),
    }
}
