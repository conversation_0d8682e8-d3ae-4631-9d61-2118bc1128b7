import type { $Fetch } from 'ofetch'
import { $fetch } from 'ofetch'

export function useHttp(): $Fetch {
    const config = useRuntimeConfig()

    // In SSR mode for internal APIs use $fetch directly without baseURL
    // Nuxt automatically handles internal requests
    if (import.meta.server && config.public.web.apiBase.startsWith('/')) {
        return $fetch.create({
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        })
    }

    // For client use regular baseURL
    return $fetch.create({
        baseURL: config.public.web.apiBase,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
    })
}
