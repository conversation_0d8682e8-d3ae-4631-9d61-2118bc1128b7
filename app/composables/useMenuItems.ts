export type MenuItem = {
    title: string
    route?: string
    isDropdown?: boolean
    items?: MenuItem[]
}

export function useMenuItems() {
    const menuItems: MenuItem[] = [
        {
            route: '/',
            title: 'Home',
        },
        {
            route: '/blog',
            title: 'Blog',
        },
        {
            route: '/about',
            title: 'About us',
        },
        {
            route: '/best-deals',
            title: 'Best Deals',
            isDropdown: true,
            items: [
                {
                    route: '/best-deals',
                    title: 'Business class',
                },
                {
                    route: '/first-class-flights',
                    title: 'First class',
                },
            ],
        },
        {
            route: '/airlines',
            title: 'Airlines',
        },
        {
            route: '/contacts',
            title: 'Contacts',
        },
    ]

    return {
        menuItems,
    }
}
