import dayjs from 'dayjs'

import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isBetween from 'dayjs/plugin/isBetween'
import { removeUndefined } from '~/helpers/UtilsHelper'

dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)
dayjs.extend(isBetween)

//

import type { Dayjs } from 'dayjs'

export type DateValue = Date | undefined
export type CalendarSingleValue = DateValue
export type CalendarRangeValue = [start: DateValue, end: DateValue]
export type CalendarValue = CalendarSingleValue | CalendarRangeValue

export type CalendarOptions = {
    /**
     * Date or range of dates to select
     */
    selectedDate?: CalendarValue

    /**
     * Date to open calendar on
     */
    displayDate?: Date

    /**
     * Displays selected date as range
     */
    range?: boolean

    /**
     * Day from which the week starts. 0-6, 0 is Sunday, 6 is Saturday
     * @values 0, 1, 2, 3, 4, 5, 6
     */
    weekStart?: number

    /**
     * A callback function to check is date is disabled
     */
    disabledDates?(date: Dayjs): boolean

    /**
     * Array of 7 headings to display
     * Pass {false} to hide headings
     */
    headings?: string[] | boolean

    /**
     * Enable hover events emitting
     */
    hoverEvents?: boolean

    /**
     * Enable click events emitting
     */
    clickEvents?: boolean

    /**
     * To programmatically set hover date when it is shared across many calendars
     */
    hoveredDate?: Date

    /**
     * Hide dates from another months in the current month view
     */
    hideOther?: boolean

    /**
     * Function to modify day display styles and content.
     */
    modifyDay?: (date: Dayjs) => { class?: string; content?: string } | undefined
}

export type CalendarEmits = {
    'dateSelect': [date: Dayjs],
    'dateHover': [date: Dayjs | undefined, event: MouseEvent | KeyboardEvent],
    'dateClick': [date: Dayjs, event: PointerEvent | KeyboardEvent],
}

//

export const defaultOptions = {
    range: false,
    weekStart: 0,
    headings: true,
    hoverEvents: false,
    clickEvents: false,
    hideOther: true,
} satisfies Partial<CalendarOptions>

export default function useCalendar(options: CalendarOptions, emit: <E extends keyof CalendarEmits, P extends CalendarEmits[E]>(event: E, ...args: P) => void) {
    const normalizedOptions = computed(() => {
        return { ...defaultOptions, ...removeUndefined(options) }
    })

    const isRangeMode = computed(() => normalizedOptions.value.range)

    const displayDate = computed<Dayjs>(() => dayjs(normalizedOptions.value.displayDate || Date.now()))

    const displayMonth = computed<number>(() => displayDate.value.month())

    const headerText = computed<string>(() => {
        return headerMonth.value + ' ' + headerYear.value
    })

    const headerMonth = computed<string>(() => {
        return displayDate.value.format('MMMM')
    })

    const headerYear = computed<string>(() => {
        return displayDate.value.format('YYYY')
    })

    const headings = computed(() => {
        const options = normalizedOptions.value

        if (options.headings === false) {
            return false
        }

        if (options.headings && Array.isArray(options.headings)) {
            return options.headings
        }

        const defaultHeadings = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa']

        if (options.weekStart === 0) {
            return defaultHeadings
        }

        return [
            ...defaultHeadings.slice(options.weekStart, defaultHeadings.length - options.weekStart + 1),
            ...defaultHeadings.slice(0, options.weekStart),
        ]
    })

    const startDay = computed<number>(() => {
        return (7 + displayDate.value.startOf('month').day() - normalizedOptions.value.weekStart) % 7
    })

    /**
     * @type-commented {{ value: number }}
     */
    const endDay = computed(() => {
        return (7 + displayDate.value.endOf('month').day() - normalizedOptions.value.weekStart) % 7
    })

    const dates = computed<Dayjs[]>(() => {
        const start = displayDate.value.startOf('month')
        const end = displayDate.value.endOf('month')

        let startDate = start
        let endDate = end

        if (startDay.value > 0) {
            startDate = start.subtract(startDay.value, 'days')
        }

        if (endDay.value < 7) {
            endDate = end.add(6 - endDay.value, 'days')
        }

        const dates = []
        let n = 0

        while (startDate.isBefore(endDate) && n++ <= 45) {
            dates.push(startDate)

            startDate = startDate.add(1, 'day')
        }

        return dates
    })

    const _hoveredDate = ref<Date>()

    const hoveredDate = computed<Date | undefined>({
        get() {
            return normalizedOptions.value.hoveredDate || _hoveredDate.value
        },
        set(value) {
            _hoveredDate.value = value
        },
    })

    const doClick = (date: Dayjs, event: PointerEvent | KeyboardEvent) => {
        if ((normalizedOptions.value.hideOther && isDateFromOtherMonth(date)) || isDisabled(date)) {
            return
        }

        emit('dateClick', date, event)
    }

    const doHover = (date: Dayjs | undefined, event: MouseEvent | KeyboardEvent) => {
        if (date) {
            if ((normalizedOptions.value.hideOther && isDateFromOtherMonth(date)) || isDisabled(date)) {
                return
            }
        }

        hoveredDate.value = date?.toDate()

        emit('dateHover', date, event)
    }

    const dateEventBindings = (date: Dayjs) => {
        const bindings: Record<string, any> = {}

        if (isDisabled(date)) {
            return bindings
        }

        if (options.clickEvents) {
            bindings.onClick = (event: PointerEvent) => doClick(date, event)
        }

        if (options.hoverEvents) {
            bindings.onMouseenter = (event: MouseEvent) => doHover(date, event)

            bindings.onMouseleave = (event: MouseEvent) => {
                hoveredDate.value = undefined

                doHover(undefined, event)
            }
        }

        return bindings
    }

    const isDateFromOtherMonth = (date: Dayjs) => {
        return date.month() !== displayMonth.value
    }

    const isDisabled = (date: Dayjs) => {
        return options.disabledDates ? options.disabledDates(date) : false
    }

    const isHovered = (date: Dayjs) => {
        if (!hoveredDate.value) {
            return false
        }

        return date.isSame(hoveredDate.value, 'day')
    }

    const isSelected = (date: Dayjs) => {
        if (!normalizedOptions.value.selectedDate || isRangeMode.value) {
            return false
        }

        return date.isSame(normalizedOptions.value.selectedDate as any, 'day')
    }

    const range = computed<CalendarRangeValue>(() => {
        const selectedDate = normalizedOptions.value.selectedDate as CalendarRangeValue | undefined

        if (!selectedDate) {
            return [undefined, undefined]
        }

        const selected: CalendarRangeValue = [selectedDate[0], selectedDate[1]]

        if (selected[0] && !selected[1] && hoveredDate.value) {
            selected[1] = hoveredDate.value
        }

        if (selected[1] && !selected[0] && hoveredDate.value) {
            selected[0] = hoveredDate.value
        }

        if (selected[0] && selected[1] && selected[0] > selected[1]) {
            return [selected[1], selected[0]]
        }

        return selected
    })

    const shouldDisplayRange = computed<boolean>(() => range.value && isRangeMode.value)

    const shouldDisplayFullRange = computed<boolean>(() => Boolean(range.value[0] && range.value[1] && isRangeMode.value))

    const isRangeStart = (date: Dayjs) => {
        return shouldDisplayRange.value && range.value[0] && date.isSame(range.value[0], 'day')
    }

    const isRangeEnd = (date: Dayjs) => {
        return shouldDisplayRange.value && range.value[1] && date.isSame(range.value[1], 'day')
    }

    const isRangeInner = (date: Dayjs) => {
        return shouldDisplayFullRange.value && date.isBetween(range.value[0], range.value[1], 'day', '[]')
    }

    const isRangeRight = (date: Dayjs) => {
        return shouldDisplayFullRange.value && isRangeStart(date) && range.value[0] && range.value[1] && range.value[0] < range.value[1]
    }

    const isRangeLeft = (date: Dayjs) => {
        return shouldDisplayFullRange.value && isRangeEnd(date) && range.value[0] && range.value[1] && range.value[0] < range.value[1]
    }

    // Focus manipulation
    const focusedIndex = ref(-1)

    const resetFocus = () => {
        focusedIndex.value = -1
    }

    const focusOffsetStart = computed(() => {
        return normalizedOptions.value.hideOther ? startDay.value : 0
    })

    const focusOffsetEnd = computed(() => {
        if (normalizedOptions.value.hideOther) {
            return 7 - (endDay.value + 1)
        }

        return 0
    })

    const focus = (changeIndex: number, event: KeyboardEvent) => {
        let newValue = focusedIndex.value + changeIndex

        const offsetStart = focusOffsetStart.value
        const offsetEnd = focusOffsetEnd.value

        // @todo Refactor this to easily understand what is going on
        const change = Math.abs(changeIndex)
        const isVertical = change === 7

        const a = newValue % change
        const b = change - a
        const c = offsetStart % change

        if (focusedIndex.value === -1) {
            if (changeIndex === +7) {
                newValue = offsetStart
            } else if (changeIndex === -7) {
                newValue = dates.value.length - 1 - offsetEnd
            } else if (changeIndex === +1) {
                newValue = offsetStart
            } else if (changeIndex === -1) {
                newValue = dates.value.length - 1 - offsetEnd
            }
        } else if (newValue < offsetStart) {
            if (isVertical) {
                newValue = dates.value.length - b

                if (newValue >= dates.value.length - offsetEnd) {
                    newValue += changeIndex
                }
            } else {
                newValue = dates.value.length + changeIndex - offsetEnd
            }
        } else if (newValue >= dates.value.length - offsetEnd) {
            if (isVertical) {
                newValue = offsetStart - c + a

                if (newValue < offsetStart) {
                    newValue += changeIndex
                }
            } else {
                newValue = newValue - (dates.value.length - offsetEnd) + offsetStart
            }
        }

        focusedIndex.value = newValue

        if (isRangeMode.value) {
            const date = dates.value.find((_, index) => index === focusedIndex.value)

            doHover(date, event)
        }
    }

    const clickFocused = (event: PointerEvent | KeyboardEvent) => {
        const date = dates.value.find((_, index) => index === focusedIndex.value)

        if (!date) {
            return
        }

        doClick(date, event)
    }

    const handleKeydown = (event: KeyboardEvent) => {
        if (!options.clickEvents || event.ctrlKey) {
            return
        }

        const stop = () => {
            event.preventDefault()
            event.stopImmediatePropagation()
        }

        if (event.code === 'ArrowDown') {
            focus(+7, event)
            stop()
        } else if (event.code === 'ArrowUp') {
            focus(-7, event)
            stop()
        } else if (event.code === 'ArrowRight') {
            focus(+1, event)
            stop()
        } else if (event.code === 'ArrowLeft') {
            focus(-1, event)
            stop()
        } else if (event.code === 'Space' || event.code === 'Enter') {
            clickFocused(event)
            stop()
        }
    }

    const handleBlur = () => {
        resetFocus()
    }

    const isFocused = (index: number) => {
        return focusedIndex.value === index
    }

    const Day = function(dayProps: {
        date: Dayjs,
        index: number,
    }) {
        const date = dayProps.date
        const index = dayProps.index

        const _isDateFromOtherMonth = isDateFromOtherMonth(date)

        const modifiers = options.modifyDay ? options.modifyDay(date) || {} : {}

        return h(
            'div',
            {
                class: {
                    'vue-calendar__date': true,
                    'vue-calendar__date--other': _isDateFromOtherMonth,
                    'vue-calendar__date--hidden': _isDateFromOtherMonth && normalizedOptions.value.hideOther,
                    'vue-calendar__date--disabled': isDisabled(date),
                    'vue-calendar__date--hover': isHovered(date),
                    'vue-calendar__date--selected': isSelected(date),
                    'vue-calendar__date--range-start': isRangeStart(date),
                    'vue-calendar__date--range-end': isRangeEnd(date),
                    'vue-calendar__date--range': isRangeInner(date),
                    'vue-calendar__date--range-right': isRangeRight(date),
                    'vue-calendar__date--range-left': isRangeLeft(date),
                    'vue-calendar__date--focused': isFocused(index),
                    ...((typeof modifiers.class === 'string' ? { [modifiers.class]: true } : modifiers.class) || {}),
                },
                ...dateEventBindings(date),
            },
            [
                h('div', { class: 'vue-calendar__date__wrapper' }, [
                    h('div', {
                        class: 'vue-calendar__date__inner',
                        innerHTML: 'content' in modifiers ? modifiers.content : date.date(),
                    }),
                ]),
            ],
        )
    }

    return {
        headerText,
        headerMonth,
        headerYear,
        headings,
        dates,
        dateEventBindings,
        isDateFromOtherMonth,
        isDisabled,
        isHovered,
        isSelected,
        isFocused,
        isRangeStart,
        isRangeEnd,
        isRangeInner,
        isRangeRight,
        isRangeLeft,
        handleKeydown,
        handleBlur,
        resetFocus,

        // Components
        Day,
    }
}
