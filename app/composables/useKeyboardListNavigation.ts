import type { MaybeRefOrGetter } from 'vue'

/**
 * @param list List of items to navigate. Each item should be a boolean value indicating if the item can be focused or not
 * @param options See TypeScript type definition for more information
 */
export function useKeyboardListNavigation(
    list: MaybeRefOrGetter<Array<boolean>>,
    options: {
        loop?: boolean,
        initialIndex?: MaybeRefOrGetter<number>
        onFocusMove?: (toIndex: number, fromIndex: number) => void,
        upKeyCode?: string,
        downKeyCode?: string,
    } = {},
) {
    const upKeyCode = options.upKeyCode ?? 'ArrowUp'
    const downKeyCode = options.downKeyCode ?? 'ArrowDown'

    const focusedIndex = ref(-1)

    function canBeFocused(index: number) {
        return toValue(list)[index]
    }

    function handleKeydown(event: KeyboardEvent) {
        if (![upKeyCode, downKeyCode].includes(event.code)) {
            return
        }

        event.preventDefault()

        if (event.code === upKeyCode) {
            moveFocusUp()
        } else if (event.code === downKeyCode) {
            moveFocusDown()
        }
    }

    const normalizeIndex = (index: number) => {
        const resultsCount = toValue(list).length

        if (index < 0) {
            return options.loop ? resultsCount - 1 : 0
        }

        if (index >= resultsCount) {
            return options.loop ? 0 : resultsCount - 1
        }

        return index
    }

    const hasFocusableItems = () => {
        return toValue(list).some(Boolean)
    }

    const getNextActiveIndex = (fromIndex: number, modifier: number): number | undefined => {
        const toIndex = normalizeIndex(fromIndex + modifier)

        if (toIndex === fromIndex) {
            return undefined
        }

        if (!canBeFocused(toIndex)) {
            return getNextActiveIndex(toIndex, modifier)
        }

        return toIndex
    }

    const getIndexToStartFrom = () => {
        return focusedIndex.value === -1 ? toValue(options.initialIndex) ?? 0 : focusedIndex.value
    }

    function moveFocusDown() {
        if (!hasFocusableItems()) {
            return
        }

        const toIndex = getNextActiveIndex(getIndexToStartFrom(), 1)

        if (toIndex === undefined) {
            return
        }

        _unsafeMoveFocusTo(toIndex)
    }

    function moveFocusUp() {
        if (!hasFocusableItems()) {
            return
        }

        const toIndex = getNextActiveIndex(getIndexToStartFrom(), -1)

        if (toIndex === undefined) {
            return
        }

        _unsafeMoveFocusTo(toIndex)
    }

    const _unsafeMoveFocusTo = (toIndex: number) => {
        const fromIndex = focusedIndex.value

        focusedIndex.value = toIndex

        options.onFocusMove?.(toIndex, fromIndex)
    }

    function moveFocusTo(index: number) {
        index = normalizeIndex(index)

        if (!canBeFocused(index) || index === focusedIndex.value) {
            return
        }

        _unsafeMoveFocusTo(index)
    }

    function resetFocus() {
        focusedIndex.value = -1
    }

    function isFocused(index: number) {
        return focusedIndex.value === index
    }

    return {
        focusedIndex,
        canBeFocused,
        isFocused,
        handleKeydown,
        moveFocusDown,
        moveFocusUp,
        moveFocusTo,
        resetFocus,
    }
}
