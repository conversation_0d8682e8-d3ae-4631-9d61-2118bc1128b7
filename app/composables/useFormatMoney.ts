export const useFormatMoney = (value: number | null = 0, currency = 'USD', options: Intl.NumberFormatOptions = {}): string => {
    if (value === null) {
        return ''
    }

    const stringValue = String(value)
    const lastCharDot = stringValue[stringValue.length - 1] === '.'

    const formatter = new Intl.NumberFormat('en-US', Object.assign({
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }, options))

    let result = formatter.format(Number(value) || 0) + (lastCharDot ? '.' : '')

    if (currency === 'USD') {
        if (value < 0) {
            result = '-$' + result.substring(1)
        } else {
            result = '$' + result
        }
    }

    return result
}
