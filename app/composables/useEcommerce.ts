import type { AnalyticsProduct, EcommerceEvent, ItineraryClass, ItineraryType } from '~/types/analytics'
import { useGoogleAnalytics } from '~/composables/useGoogleAnalytics'

/**
 * Composable for Enhanced Ecommerce tracking
 * Provides methods for tracking product interactions and conversions
 */
export function useEcommerce() {
    const { sendEventSafe, createProduct, getClassDisplayName } = useGoogleAnalytics()

    /**
     * Track product detail view
     */
    const trackProductDetail = async (
        routeName: string,
        routeId: string,
        price: number,
        tripType: ItineraryType,
        serviceClass: ItineraryClass,
        position: number = 0,
    ) => {
        const product = createProduct(
            routeName,
            routeId,
            price,
            tripType,
            serviceClass,
            position,
        )

        const event: EcommerceEvent = {
            event: 'gtm-ee-event',
            gtmUaEventCategory: 'Enhanced Ecommerce',
            gtmUaEventAction: 'Product Detail',
            gtmUaEventNonInteraction: 'True',
            ecommerce: {
                detail: {
                    actionField: {
                        list: getClassDisplayName(serviceClass),
                    },
                    products: [product],
                },
            },
        }

        await sendEventSafe(event)
    }

    /**
     * Track add to cart event
     */
    const trackAddToCart = async (
        routeName: string,
        routeId: string,
        price: number,
        tripType: ItineraryType,
        serviceClass: ItineraryClass,
        quantity: number = 1,
        position: number = 0,
    ) => {
        const product = createProduct(
            routeName,
            routeId,
            price,
            tripType,
            serviceClass,
            position,
            quantity,
        )

        const event: EcommerceEvent = {
            event: 'gtm-ee-event',
            gtmUaEventCategory: 'Enhanced Ecommerce',
            gtmUaEventAction: 'Add To Cart',
            gtmUaEventNonInteraction: 'True',
            ecommerce: {
                add: {
                    actionField: {
                        list: getClassDisplayName(serviceClass),
                    },
                    products: [product],
                },
            },
        }

        await sendEventSafe(event)
    }

    /**
     * Track product click event
     */
    const trackProductClick = async (
        routeName: string,
        routeId: string,
        price: number,
        tripType: ItineraryType,
        serviceClass: ItineraryClass,
        position: number = 0,
    ) => {
        const product = createProduct(
            routeName,
            routeId,
            price,
            tripType,
            serviceClass,
            position,
        )

        const event: EcommerceEvent = {
            event: 'gtm-ee-event',
            gtmUaEventCategory: 'Enhanced Ecommerce',
            gtmUaEventAction: 'Product Click',
            gtmUaEventNonInteraction: 'True',
            ecommerce: {
                click: {
                    actionField: {
                        list: getClassDisplayName(serviceClass),
                    },
                    products: [product],
                },
            },
        }

        await sendEventSafe(event)
    }

    /**
     * Track product impressions
     */
    const trackProductImpressions = async (products: AnalyticsProduct[]) => {
        const event: EcommerceEvent = {
            event: 'gtm-ee-event',
            gtmUaEventCategory: 'Enhanced Ecommerce',
            gtmUaEventAction: 'Product Impressions',
            gtmUaEventNonInteraction: 'True',
            ecommerce: {
                impressions: products,
            },
        }

        await sendEventSafe(event)
    }

    /**
     * Track purchase/conversion
     */
    const trackPurchase = async (
        transactionId: string,
        revenue: number,
        products: AnalyticsProduct[],
        affiliation: string = 'TravelBusinessClass',
        tax: number = 0,
        shipping: number = 0,
        coupon?: string,
    ) => {
        const event: EcommerceEvent = {
            event: 'gtm-ee-event',
            gtmUaEventCategory: 'Enhanced Ecommerce',
            gtmUaEventAction: 'Purchase',
            gtmUaEventNonInteraction: 'True',
            ecommerce: {
                purchase: {
                    actionField: {
                        id: transactionId,
                        affiliation,
                        revenue,
                        tax,
                        shipping,
                        coupon,
                    },
                    products,
                },
            },
        }

        await sendEventSafe(event)
    }

    /**
     * Track multiple products from search results or listing
     */
    const trackProductList = async (
        routes: Array<{
            name: string
            id: string
            price: number
            tripType: ItineraryType
            serviceClass: ItineraryClass
        }>,
    ) => {
        const products = routes.map((route, index) =>
            createProduct(
                route.name,
                route.id,
                route.price,
                route.tripType,
                route.serviceClass,
                index,
            ),
        )

        await trackProductImpressions(products)
    }

    /**
     * Track class change (when user switches between Premium/Business/First)
     */
    const trackClassChange = async (
        routeName: string,
        routeId: string,
        newPrice: number,
        tripType: ItineraryType,
        newServiceClass: ItineraryClass,
    ) => {
        // Track as a new product detail view with the new class
        await trackProductDetail(
            routeName,
            routeId,
            newPrice,
            tripType,
            newServiceClass,
        )
    }

    /**
     * Helper to create product from route data
     */
    const createProductFromRoute = (
        fromCode: string,
        toCode: string,
        price: number,
        tripType: ItineraryType,
        serviceClass: ItineraryClass,
        position: number = 0,
    ): AnalyticsProduct => {
        const routeName = `${fromCode} - ${toCode}`
        const routeId = `${fromCode}2${toCode}`

        return createProduct(
            routeName,
            routeId,
            price,
            tripType,
            serviceClass,
            position,
        )
    }

    return {
        // Main tracking methods
        trackProductDetail,
        trackAddToCart,
        trackProductClick,
        trackProductImpressions,
        trackPurchase,

        // Helper methods
        trackProductList,
        trackClassChange,
        createProductFromRoute,
    }
}
