.menu-modal {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    padding-left: 2.5rem;
    max-width: 100%;
    display: flex;

    &__container {
        position: relative;
        width: 100vw;
        max-width: 28rem;
    }



    &__close {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        padding-top: 1.25rem;
        padding-left: 1.25rem;

        &__button {
            display: flex;
            width: 3rem;
            height: 3rem;
            background-color: rgba(229, 231, 235, 0.2);
            color: var(--theme-color-base-white);
            border-radius: 9999px;
            outline: none;
            align-items: center;
            justify-content: center;
        }
    }

    &__content {
        height: 100%;
        display: flex;
        flex-direction: column;
        padding-top: 4rem;
        padding-bottom: 4rem;
        background-color: #282f36;
        color: var(--theme-color-base-white);
        border-top-left-radius: 2.5rem;
        border-bottom-left-radius: 2.5rem;
        overflow-y: scroll;
        overscroll-behavior: contain;

        &__padding {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1 1 0;
            padding-left: 4rem;
            padding-right: 4rem;
            padding-top: 1.5rem;
        }
    }

    &__menu {
        font-size: 1.5rem;
        line-height: 1.375;
        font-weight: 700;
        display: flex;
        flex-direction: column;
        flex: 1 1 0;

        > * + * {
            margin-top: 2rem;
        }

        &__item {
            &--active {
                opacity: 0.5;
            }

        }
    }

    &__agent {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 2rem;

        &__avatar {
            flex: none;
            position: relative;
            width: 2.5rem;
            height: 2.5rem;

            &__online {
                position: absolute;
                top: 0.125rem;
                right: -0.375rem;
                width: 1rem;
                height: 1rem;
                border-width: 3px;
                border-style: solid;
                border-color: #282f36;
                background-color: #00c908;
                border-radius: 9999px;

                @media (min-width: 1280px) {
                    width: 1rem;
                    height: 1rem;
                    border-width: 3px;
                }
            }
        }

        &__name {
            margin-top: 2px;
        }

        &__contact {
            display: flex;
            align-items: center;
            margin-top: 0.25rem;
        }

        &__call {
            color: #4a94ec;
            font-size: 16px;
            text-transform: none;
            margin-top: 1rem;
            letter-spacing: unset;
        }
    }
}
