.header {
    position: absolute;
    width: 100%;
    padding-top: 1rem;
    padding-bottom: 1rem;
    z-index: 11;
    @media (min-width: 1024px) {
        padding-top: 2rem;
        padding-bottom: 5rem;
    }

    &__main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 1.25rem;
        padding-right: 1.25rem;

        @media (min-width: 1024px) {
            padding-left: 2rem;
            padding-right: 2rem;
        }
    }

    &__logo {
        display: flex;
        align-items: start;
        margin-top: -0.25rem;

        @media (min-width: 1024px) {
            margin-top: -1rem;
        }

        @media (min-width: 1536px) {
            flex: 1;
        }
    }

    &__secondary {
        padding: 0 1.5rem 2rem 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;

        @media (max-width: 767px) {
            padding-bottom: 1.5rem;
        }
    }

    &__description {
        text-align: center;

        @media (min-width: 1024px) {
            display: none;
        }

        &__title {
            font-weight: 400;
            font-size: 0.75rem;
            line-height: 1rem;
            color: var(--theme-color-base-white);
        }
    }

    &__menu {
        display: none;
        color: var(--theme-color-base-white);
        white-space: nowrap;

        @media (min-width: 1024px) {
            display: flex;
            gap: 2.5rem;
            align-items: center;
        }

        @media (min-width: 1536px) {
            flex: 1;
            text-align: center;
        }

        &__item {
            position:  relative;
            font-size: 14px;
            color: currentColor;
            padding: 4px;
            display: flex;
            align-items: center;
            column-gap: 0.25rem;
            cursor: pointer;

            &:hover,
            &.\--active {
                opacity: .5;

                &:after {
                    position:  absolute;
                    content: '';
                    width: 19px;
                    height: 1px;
                    background-color:  currentColor;
                    bottom: -2px;
                    left: 50%;
                    margin-left: -9.5px;
                }
            }

            &__icon {
                transition: 0.2s;
                margin-top: 4px;
            }
        }

        &__submenu {
            position: absolute;
            top: 100%;
            left: -25%;
            background-color: white;
            padding: 0.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            z-index: 10;
            min-width: 160px;
            margin-top: 0.75rem;

            &:before{
                content: "";
                position: absolute;
                top: -0.3rem;
                left: 48%;
                width: 0.65rem;
                height: 0.65rem;
                background-color: inherit;
                border-radius: 0.125rem;
                clip-path: polygon(0 0, 100% 100%, 0 100%);
                transform: rotate(135deg);
            }

            &__item {
                display: block;
                padding: 0.375rem 0.5rem 0.5rem;
                white-space: nowrap;
                color: #111;
                font-size: 14px;
                transition: background 0.2s;

                &:hover {
                    background-color: #f3f3f3;
                    border-radius: 0.375rem;
                }
            }
        }
    }

    &__right {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        @media (min-width: 1536px) {
            flex: 1;
        }

        &__call {
            display: none;

            @media (min-width: 1024px) {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }

            @media (min-width: 1536px) {
                flex: 1;
            }
        }

        &__info {
            display: none;
            margin-right: 1rem;
            color: var(--theme-color-base-white);
            text-align: right;

            @media (min-width: 1280px) {
                display: block;
            }

            &-text {
                font-size: 0.875rem;
                display: block;
            }
        }

        &__phone-link {
            display: flex;
            align-items: center;
            font-weight: bold;
            font-size: 1.125rem;
            color: var(--theme-color-base-white);
            text-decoration: none;
        }

        &__phone-icon {
            width: 1rem;
            margin-right: 0.25rem;
            fill: currentColor;
        }

        &__call-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 9999px;
            background-color: var(--theme-color-base-white);
            color: var(--theme-color-brand);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -4px rgba(0, 0, 0, 0.1);
            outline: none;
            cursor: pointer;
            transition: background 0.3s ease;

            &:hover {
                color: var(--theme-color-base-white);
                background: linear-gradient(to right, var(--theme-color-brand-gradient-from), var(--theme-color-brand-gradient-to));
            }
        }
    }

    &__agent {
        display: flex;
        gap: 1.25rem;
        font-size: 1rem;
        text-align: right;
        align-items: center;
        flex-shrink: 0;
        min-width: 0;
        width: fit-content;

        @media (max-width: 767px) {
            gap: 0.75rem;
        }

        &__phone {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.25rem;

            @media (max-width: 767px) {
                font-size: 12px;
            }
        }

        &__name {
            @apply font-body2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            @media (max-width: 767px) {
                font-size: 12px;
            }
        }

        &__info {
            min-width: 0;
        }

        &__avatar {
            position: relative;
            flex-shrink: 0;

            &__image {
                height: 60px;
                width: 60px;
                border-radius: 50%;

                @media (max-width: 767px) {
                    width: 33px;
                    height: 33px;
                }
            }

            &::before {
                content: '';
                position: absolute;
                top: 9px;
                right: 0;
                height: 18px;
                width: 18px;
                background: var(--theme-color-danger);
                border: 3px solid var(--theme-color-brand-gradient-to);
                border-radius: 50%;

                @media (max-width: 767px) {
                    top: 5px;
                    width: 7px;
                    height: 7px;
                    border-width: 1px;
                }
            }

            &--online {
                &::before {
                    background: var(--theme-color-success);
                }
            }
        }
    }

    &__menu-button {
        height: 48px;
        width: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #F4F6F7;
        flex-shrink: 0;
        margin-left: 0.5rem;
        margin-right: -0.5rem;

        @media (min-width: 1024px) {
            display: none;
        }
    }
}

.logo {
    font-weight: bold;
    font-size: 30px;
    width: fit-content;
    color: var(--theme-color-base-white);
}
