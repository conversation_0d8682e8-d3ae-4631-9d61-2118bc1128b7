.step-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    height: 100%;

    /*.page--readonly & {*/
    /*    height: auto;*/
    /*}*/

    &--ticket-protection {
        @media (max-width: 1500px) {
            grid-template-columns: 1fr;
        }
    }

    &--payment {
        grid-template-columns: 58% 42%;
    }

    &--itinerary {
        @media (max-width: 767px) {
            .section__title {
                display: none;
            }
        }
    }

    &--complete {
        grid-template-columns: 2fr minmax(560px, 1fr);
    }

    @media (max-width: 1139px) {
        grid-template-columns: 1fr;
    }

    &__main {
        padding: 2rem 4rem;
        min-width: 0;
        border-top-left-radius: var(--theme-border-radius-layout);
        background-color: #fff;

        @media (max-width: 1139px) {
            border-top-right-radius: var(--theme-border-radius-layout);
            padding: 2rem;
        }

        @media (max-width: 767px) {
            padding: 1.5rem 0.75rem;
        }

        &.col-span-full {
            border-top-right-radius: var(--theme-border-radius-layout);
        }

        .section:last-child {
            margin-bottom: 0;
        }
    }

    &__aside {
        border-top-right-radius: var(--theme-border-radius-layout);
        background-color: #F4F6F7;
        padding: 2.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4.5rem;
        justify-content: flex-start;
        min-width: 0;

        @media (max-width: 1139px) {
            padding: 2rem;

            .step-layout--itinerary & {
                gap: 2rem;

                .aside-next-step {
                    display: none;
                }

                .aside-contacts .accreditation {
                    margin-top: 0;
                }
            }

            .step-layout--itinerary &,
            .step-layout--passengers &,
            .step-layout--ticket-protection &,
            .step-layout--payment & {
                background-color: unset;
            }

            @media print {
                .step-layout--itinerary &,
                .step-layout--passengers &,
                .step-layout--ticket-protection & {
                    .aside-contacts__agent {
                        display: none;
                    }
                }
            }

            .step-layout--payment & {
                gap: 2rem;
            }
        }

        @media (max-width: 767px) {
            padding: 1.5rem 0.75rem;
        }
    }

    &__block {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4.5rem;
        justify-content: flex-start;
        width: 100%;

        @media (max-width: 1139px) {
            background-color: #F4F6F7;
            padding: 2rem 1rem;
            border-radius: 10px;
        }
    }

    &__header {
        position: relative;
        text-align: center;
        margin-bottom: 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 72px;
        padding: 0 3.5rem;

        @media (max-width: 1139px) {
            min-height: 50px;
        }
    }

    &__title {
        font-size: 36px;
        font-weight: bold;
        line-height: 1.3;
        letter-spacing: -0.01562em;

        @media (max-width: 767px) {
            font-size: 19px;
        }

        &--small {
            font-size: 25px;

            @media (max-width: 767px) {
                font-size: 19px;
            }
        }
    }

    &__back-button {
        position: absolute;
        top: 50%;
        left: 0;
        margin-top: -23px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--theme-color-info);
        background: color(from var(--theme-color-info) srgb r g b / 0.1);
        border-radius: 23px;
        cursor: pointer;
        width: 46px;
        height: 46px;
        transition: box-shadow 0.3s ease;

        &:hover {
            box-shadow: 0px 3px 14px rgba(0, 0, 0, 0.15);
        }

        svg {
            height: 18px;
            width: 18px;
        }

        .page--readonly & {
            display: none;
        }
    }
}
