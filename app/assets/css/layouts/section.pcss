.section {
    margin-top: 4rem;
    text-align: center;
    position: relative;

    @media (min-width: 1024px) {
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        max-width: 56rem;
    }

    @media (min-width: 1280px) {
        max-width: 72rem;
    }

    @media (min-width: 1536px) {
        max-width: 92rem;
    }

    &__header {
        margin-bottom: 1.5rem;
    }

    &__headline {
        text-align: center;
        color: var(--theme-color-brand);
        text-transform: uppercase;
        font-size: 0.75rem;
        line-height: 1rem;
        font-weight: bold;
        letter-spacing: 0.075rem;
    }

    &__title {
        font-size: 24px;
        font-weight: bold;
        line-height: 1.3;
        letter-spacing: -0.00833em;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        gap: 1.25rem;

        svg {
            flex-shrink: 0;
        }

        @media (max-width: 767px) {
            font-size: 17px;
        }
    }

    &__subtitle {
        @apply font-body1;
        text-align: center;
    }

    &--no-margin {
        margin-top: 0;

        @media (min-width: 1024px) {
            margin-top: 4rem;
        }
    }
}
