.footer {
    padding: 6rem 1.5rem 2.75rem;

    @media (min-width: 1024px) {
        padding: 8rem 0 6rem;
    }

    &__section {
        @media (min-width: 1024px) {
            width: 100%;
            margin-left: auto;
            margin-right: auto;
            max-width: 56rem;
        }

        @media (min-width: 1280px) {
            max-width: 72rem;
        }

        @media (min-width: 1536px) {
            max-width: 88rem;
        }

        &__container {
            @media (min-width: 1024px) {
                display: grid;
                grid-template-columns: repeat(17, minmax(0, 1fr));
                gap: 2rem;
            }

            @media (min-width: 1280px) {
                gap: 4rem;
            }
        }
    }

    &__contacts {
        @media (min-width: 1024px) {
            grid-column: span 6 / span 6;
        }

        @media (min-width: 1280px) {
            grid-column: span 5 / span 5;
        }

        &__title {
            font-size: 24px;
            font-weight: bold;
            line-height: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;

            @media (min-width: 1024px) {
                display: none;
            }
        }

        &__link-items {
            display: flex;
            justify-content: center;
            margin-top: 2.25rem;

            > * + * {
                margin-left: 1.75rem;
            }

            @media (min-width: 1024px) {
                justify-content: flex-start;
            }
        }

        &__item {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 3rem;

            @media (min-width: 1024px) {
                flex-direction: row;
                margin-top: 2rem;
            }

            &__icon-container {
                flex-shrink: 0;

                &__icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 6rem;
                    height: 6rem;
                    background-color: #ffffff;
                    border-radius: 9999px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* adjust for your 'shadow-light' */

                    @media (min-width: 1024px) {
                        width: 3rem;
                        height: 3rem;
                    }

                    svg {
                        width: 2.5rem;
                        height: 2.5rem;


                        @media (min-width: 1024px) {
                            width: 1.5rem;
                            height: 1.5rem;
                        }
                    }
                }
            }

            &__info-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-top: 1.25rem;

                @media (min-width: 1024px) {
                    align-items: flex-start;
                    margin-left: 1rem;
                    margin-top: 0;
                }

                &__title {
                    text-transform: uppercase;
                    font-size: 0.75rem;
                    line-height: 1rem;
                    color: #838d95;
                    letter-spacing: 0.1em;
                    font-weight: 700;

                    @media (min-width: 1024px) {
                        font-size: 11px;
                        line-height: 0.875rem;
                    }
                }

                &__value {
                    font-weight: bold;
                    display: inline-block;
                    color: var(--theme-color-text-black);

                    &--brand {
                        color: var(--theme-color-brand);

                        @media (min-width: 1024px) {
                            font-size: 0.875rem;
                            line-height: 1.25rem;
                        }
                    }
                }
            }

            &__link {
                flex: none;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 2.75rem;
                height: 2.75rem;
                background-color: var(--theme-color-base-white);
                border-radius: 0.75rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                color: #3b82f6;

                svg {
                    width: 1.5rem;
                    height: 1.5rem;
                }
            }
        }
    }

    &__info {
        margin-top: 1.75rem;
        text-align: center;

        @media (min-width: 1024px) {
            margin-top: 0;
            text-align: left;
            grid-column: span 11 / span 11;
        }

        @media (min-width: 1280px) {
            grid-column: span 12 / span 12;
        }

        &__title {
            display: none;
            font-size: 24px;
            font-weight: bold;
            line-height: 2rem;
            color: var(--theme-color-text-black);

            @media (min-width: 1024px) {
                display: block;
            }
        }

        &__terms {
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--theme-color-text-black);

            @media (min-width: 1024px) {
                margin-top: 1.5rem;
            }
        }

        &__company {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 2.75rem;
            font-size: 0.875rem;
            line-height: 1;

            @media (min-width: 1024px) {
                justify-content: flex-start;
            }
        }

        &__partners {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;

            @media (min-width: 1024px) {
                justify-content: flex-start;
                gap: 2rem;
            }
        }
    }
}
