.company-description {
    padding-left: 1.5rem;
    padding-right: 1.5rem;

    @media (min-width: 1024px) {
        padding-left: 0;
        padding-right: 0;
        background-color: var(--theme-color-base-white);
        border-radius: 2.5rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    &__container {
        text-align: center;

        @media (min-width: 1024px) {
            text-align: left;
            display: grid;
            grid-template-columns: repeat(10, 1fr);
        }
    }

    &__info {
        color: var(--theme-color-text-black);
        letter-spacing: 0.01px;

        @media (min-width: 1024px) {
            grid-column: span 4 / span 4;
            padding-top: 4rem;
            padding-left: 3rem;
            padding-bottom: 3rem;
        }

        @media (min-width: 1280px) {
            padding-top: 5rem;
            padding-left: 4rem;
            padding-bottom: 2.75rem;
        }

        @media (min-width: 1536px) {
            padding: 6rem 3rem 3.5rem 6rem;
        }

        &__title {
            font-size: 1.125rem;
            line-height: 1.75rem;

            @media (min-width: 1280px) {
                font-size: 1.25rem;
                line-height: 1.75rem;
            }

            @media (min-width: 1536px) {
                font-size: 1.5rem;
                line-height: 2rem;
            }
        }

        &__description {
            margin-top: 1rem;

            @media (min-width: 1024px) {
                font-size: 1.125rem;
                line-height: 1.75rem;
            }

            @media (min-width: 1280px) {
                font-size: 1.25rem;
                line-height: 1.75rem;
            }

            @media (min-width: 1536px) {
                font-size: 1.5rem;
                line-height: 2rem;
            }
        }

        &__action {
            display: none;
            margin-top: 2rem;

            @media (min-width: 1024px) {
                display: inline-flex;

            }
        }
    }

    &__stewardess {
        position: relative;

        @media (min-width: 1024px) {
            grid-column: span 3 / span 3;
            display: flex;
            justify-content: center;
        }

        &__container {
            text-align: center;

            @media (min-width: 1024px) {
                position: absolute;
                bottom: 0;
            }

            &__image {
                &--desktop {
                    display: none;

                    @media (min-width: 1024px) {
                        display: inline-block;

                    }
                }
                &--mobile {
                    display: inline-block;

                    @media (min-width: 1024px) {
                        display: none;
                    }
                }
            }
        }

    }
        &__support {
            margin-top: 2rem;
            color: var(--theme-color-text-black);

            @media (min-width: 1024px) {
                margin-top: 0;
                grid-column: span 3 / span 3;
                padding-top: 4rem;
                padding-right: 2rem;
                padding-bottom: 3rem;
            }

            @media (min-width: 1280px) {
                padding-top: 5rem;
                padding-right: 2.75rem;
            }

            @media (min-width: 1536px) {
                padding-top: 6rem;
                padding-right: 3.75rem;
                padding-left: 5rem;
            }

            &__headline {

            }

            &__title {
                font-weight: bold;
                @media (min-width: 1024px) {
                    font-size: 1.125rem;
                    line-height: 1.75rem;
                }

                @media (min-width: 1280px) {
                    font-size: 1.25rem;
                    line-height: 1.75rem;
                }

                @media (min-width: 1536px) {
                    font-size: 1.5rem;
                    line-height: 2rem;
                }
            }

            &__description {
                letter-spacing: 0.1px;
                @media (min-width: 1024px) {
                    font-size: 1rem;
                    line-height: 1.5rem;
                }

                @media (min-width: 1536px) {
                    font-size: 1.125rem;
                    line-height: 1.75rem;
                }
            }
        }
}
