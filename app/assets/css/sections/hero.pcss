.section-hero {
    position: relative;
    margin-bottom: 1.5rem;

    min-height: 100dvh;

    @media (min-width: 640px) {
        margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
        margin-bottom: 2.5rem;
    }

    @media (min-width: 1024px) {
        margin-bottom: 2.75rem;
    }

    &__video {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 0;
        margin: 0;
        border-bottom-left-radius: 3rem;
        border-bottom-right-radius: 3rem;
    }

    &__content {
        position: relative;
        z-index: 10;
        padding: 4rem 0.75rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;

        @media (min-width: 640px) {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        @media (min-width: 768px) {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        @media (min-width: 1024px) {
            padding-left: 2rem;
            padding-right: 2rem;
        }

        &__form-wrapper {
            width: 100%;
            max-width: 95%;
            margin: 4rem auto 0 auto;

            @media (min-width: 640px) {
                max-width: 90%;
            }

            @media (min-width: 768px) {
                max-width: 42rem;
            }

            @media (min-width: 1024px) {
                max-width: 48rem;
            }

            @media (min-width: 1280px) {
                max-width: 72rem;
            }
        }
    }
}
.section-hero__overlay {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
    pointer-events: none;
    border-bottom-left-radius: 3rem;
    border-bottom-right-radius: 3rem;
}

