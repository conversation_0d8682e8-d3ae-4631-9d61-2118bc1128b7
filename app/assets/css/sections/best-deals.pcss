.best-deals {
    display: flex;
    flex-direction: column;

    &__items-container {
        position: relative;
        margin-top: 2.5rem;
        padding-left: 0.625rem;
        padding-right: 0.625rem;
        display: flex;
        flex-direction: column;
        gap: 0.625rem;

        @media (min-width: 768px) {
            gap: 0;
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            column-gap: 0.625rem;
            row-gap: 0.625rem;
        }

        @media (min-width: 1024px) {
            grid-template-columns: repeat(3, minmax(0, 1fr));
            column-gap: 1.25rem;
        }

        @media (min-width: 1280px) {
            column-gap: 1.5rem;
            padding-left: 0;
            padding-right: 0;
        }
    }

    &__view-more {
        display: flex;
        justify-content: center;
        padding-left: 0.625rem;
        padding-right: 0.625rem;
        margin-top: 2rem;

        &__link {
            position: relative;
            width: 100%;
            max-width: 24rem;
            display: flex;
            justify-content: center;
            &__chevron {
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                display: flex;
                align-items: center;
                justify-content: center;

            }
        }
    }

    &__item {
        display: block;
        transition: transform 300ms;
        &:hover {
            transform: translateY(-1rem);
        }

        &__card {
            position: relative;
            display: flex;
            padding-bottom: 0;

            @media (min-width: 1024px) {
                flex-direction: column;
                padding-bottom: 5rem;
            }

            &__place {
                position: relative;
                flex-grow: 0;
                flex-shrink: 0;
                width: 10rem;
                height: 8rem;
                border-radius: 1rem;
                overflow: hidden;

                @media (min-width: 1024px) {
                    width: 100%;
                    height: 15rem;
                    border-radius: 2.5rem;
                }

                @media (min-width: 1280px) {
                    height: 20rem;
                }

                &__image {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    max-width: none;
                    object-fit: cover;
                }

                &__shadow {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    background-image: linear-gradient(to top, #1e293b, transparent, transparent);
                }
            }

            &__description {
                display: flex;
                flex-direction: column;
                align-items: stretch;
                flex-grow: 1;
                margin-left: -2rem; /* -ml-8 */
                z-index: 10;

                @media (min-width: 1024px) {
                    margin-left: 0;
                    position: absolute;
                    bottom: 0;
                    flex-grow: 0;
                }

                @media (min-width: 1280px) {
                    width: 100%;
                }

                &__container {
                    display: flex;
                    flex: 1 1 0;
                    background-color: #ffffff;
                    border-radius: 1rem;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                    align-items: center;
                    padding-left: 1.5rem;
                    padding-right: 0.75rem;
                    text-align: left;

                    @media (min-width: 1024px) {
                        flex: 1 1 auto;
                        height: 8rem;
                        border-radius: 2.5rem;
                        padding-left: 2rem;
                    }

                    &__info {
                        display: flex;
                        flex-direction: column;
                        flex-grow: 1;
                        padding-right: 0.5rem;

                        &__profit {
                            font-weight: bold;
                            font-size: 0.75rem;
                            color: #6b7280;
                            margin-bottom: 0.25rem;

                            &__amount {
                                color: #00c908;
                            }
                        }

                        &__title {
                            line-height: 1;
                            font-size: 1.125rem;
                            margin-top: auto;
                            margin-bottom: auto;
                            transition: all 0.2s;
                            color: var(--theme-color-text-black);
                            font-weight: bold;
                            .group:hover & {
                                color: #3b82f6; /* Tailwind's blue-450 (approximate) */
                            }
                        }

                        &__price {
                            color: var(--theme-color-brand);
                            font-weight: bold;
                            letter-spacing: 0;
                            font-size: 1.25rem;
                            margin-top: 0.25rem;
                            &__currency {
                                font-size: 0.625rem;
                                -webkit-background-clip: text;
                                background-clip: text;
                            }
                        }
                    }

                    &__view {
                        display: flex;
                        flex-shrink: 0;
                        align-items: center;

                        &__icon {
                            rotate: -90deg;
                            width: 1.5rem;
                            height: 1.5rem;
                            color: #374151;
                        }
                    }
                }
            }
        }
    }
}
