.container {
    width: 100%;

    @media (min-width: 640px) {
        max-width:640px;
    }

    @media (min-width: 768px) {
        max-width:768px;
    }

    @media (min-width: 1024px) {
        max-width:1024px;
    }

    @media (min-width: 1280px) {
        max-width:1280px;
    }

    @media (min-width: 1536px) {
        max-width:1536px;
    }

    @media (min-width: 1920px) {
        max-width:1920px;
    }
}

.deals-section {
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-top: 2rem;

    @media (min-width: 1024px) {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    @media (min-width: 1536px) {
        margin-top: 4rem;
        padding-left: 2rem;
        padding-right: 2rem;
    }

    &__list {
        margin-top: 0.25rem;

        @media (min-width: 1024px) {
            display: flex;
            margin-left: -1rem;
            margin-right: -1rem;

        }

        &__group {

            @media (min-width: 1024px) {
                flex: 1 1 0;
                border-radius: 2.5rem;
                margin-left: 1rem;
                margin-right: 1rem;
            }

            &__container {
                > * + * {
                    margin-top: 0.375rem;
                }
                @media (min-width: 1024px) {
                    display: flex;
                    margin-left: -1rem;
                    margin-right: -1rem;
                }
            }
        }
    }

    &__group-item {
        background-color: #ffffff;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        border-radius: 0.75rem;

        @media (min-width: 1024px) {
            flex: 1 1 0;
            border-radius: 1.75rem;
            margin-left: 1rem;
            margin-right: 1rem;
        }

        &__accordion {
            display: flex;
            align-items: center;
            justify-content: space-between;
            text-transform: uppercase;
            font-weight: 700;
            font-size: 0.75rem;
            line-height: 1.375;
            letter-spacing: 0.1em;
            padding: 1rem 1rem 1rem 1.5rem;
            width: 100%;

            @media (min-width: 1024px) {
                display: none;
            }

            &:hover {
                color: var(--theme-color-brand);
            }

        }

        &__container {
            max-height: 0;
            overflow: hidden;
            transition-property: all;
            transition-duration: 300ms;

            &--opened {
                max-height: 24rem;
            }

            @media (min-width: 1024px) {
                max-height: unset;
            }

            &__content {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
                padding-bottom: 0.5rem;

                @media (min-width: 1024px) {
                    padding: 2.25rem 2.25rem 0.5rem;
                }

                @media (min-width: 1280px) {
                    padding: 3.5rem 3.5rem 1rem;
                }

                &__heading {
                    display: none;
                    font-size: 0.75rem;
                    line-height: 1rem;

                    @media (min-width: 1024px) {
                        display: block;
                    }
                }

                &__list {
                    > * + * {
                        margin-top: 0.375rem;
                    }

                    @media (min-width: 1024px) {
                        margin-top: 1rem;

                        > * + * {
                            margin-top: 0.375rem;
                        }
                    }

                    &__item {
                        &:hover{
                            color:var(--theme-color-brand);
                        }
                    }
                }

                &__group {
                    margin-top: 1.5rem;
                    padding-top: 1rem;
                    padding-bottom: 1rem;
                    border-top: 1px solid #e4e4e4;

                    &__title {
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        text-transform: uppercase;
                        font-weight: 700;
                        font-size: 0.75rem;
                        letter-spacing: 0.05em;

                        &:hover {
                            color: var(--theme-color-brand);
                        }
                    }
                }
            }
        }
    }
}
