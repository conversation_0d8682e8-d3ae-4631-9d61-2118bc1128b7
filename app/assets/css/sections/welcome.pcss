.welcome {
    display: flex;
    flex-direction: column;
    border-radius: 3rem;
    color: #d1d5db;

    @media (min-width: 1024px) {
        flex-direction: row;
        background-color: var(--theme-color-secondary-900);
    }

    &__content {
        text-align: center;
        padding: 0 1.25rem;

        @media (min-width: 1024px) {
            flex: 1;
            text-align: left;
            padding: 4rem 5rem 4rem 5rem;
        }

        @media (min-width: 1536px) {
            padding-left: 6rem;
            padding-right: 10rem;
        }
    }

    &__tag {
        display: block;

        &--lower {
            margin-top: 2.75rem;
        }
    }

    &__title {
        font-size: 2.25rem;
        font-weight: bold;
        line-height: 2.5rem;

        color: var(--theme-color-text-black);

        @media screen and (min-width: 1024px) {
            color: var(--theme-color-text-white);
        }
    }

    &__description {
        margin-top: 0.875rem;
        font-size: 1.125rem;
        line-height: 1.75rem;
        color: var(--theme-color-text-black);

        @media (min-width: 1024px) {
            color: var(--theme-color-text-white);
            font-size: 1rem;
            line-height: 1.5rem;
        }

        @media (min-width: 1536px) {
            font-size: 1.125rem;
            line-height: 1.5rem;
        }
    }

    &__list {
        list-style: none;
        padding: 0;
        margin-top: 1rem;
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        text-align: left;
        font-size: 1.125rem;

        @media (min-width: 1024px) {
            font-size: 1rem;
        }

        @media (min-width: 1536px) {
            font-size: 1.125rem;
        }
    }

    &__item {
        display: flex;
        align-items: flex-start;
        line-height: 1.5rem;
        color: var(--theme-color-text-black);

        @media (min-width: 1024px) {
            margin-left: -2.5rem;
            color: var(--theme-color-text-white);
        }
    }

    &__icon {
        flex-shrink: 0;
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 2rem;
        margin-top: 0.125rem;
        stroke: #000000;

        @media (min-width: 1024px) {
            margin-right: 1rem;
            stroke: #ffffff;
        }
    }

    &__button {
        display: none;

        @media (min-width: 1024px) {
            display: inline-flex;
            margin-top: 3rem;
        }
    }

    &__image {
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
        margin-left: 0.625rem;
        margin-right: 0.625rem;
        margin-top: 2.75rem;
        border-radius: 2.5rem;
        height: 13rem;

        @media (min-width: 768px) {
            height: 18rem;
        }

        @media (min-width: 1024px) {
            margin-top: 0;
            margin-left: 0;
            margin-right: 0;
            box-shadow: none;
            height: auto;
            width: 100%;
            max-width: 24rem;
            flex: none;
            border-radius: 3rem;
        }

        @media (min-width: 1280px) {
            max-width: 32rem;
        }

        @media (min-width: 1536px) {
            max-width: 36rem;
        }

        img {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            max-width: none;
            object-fit: cover;
            border-radius: 0;

            @media (min-width: 1024px) {
                border-radius: 3rem;
            }
        }
    }
}
