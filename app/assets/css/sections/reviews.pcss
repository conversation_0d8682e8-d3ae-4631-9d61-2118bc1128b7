.reviews {
    display: flex;
    flex-direction: column;
    align-items: center;

    &__total {
        text-align: center;

        &__title {
            font-size: 24px;
            font-weight: bold;
            line-height: 2rem;
            color: var(--theme-color-text-black);

            &__gradient {
                background: var(--text-gradient);
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                -webkit-text-fill-color: transparent;
            }
        }
    }

    &__trustpilot {
        margin-top: 2rem;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;

        &__image {
            max-width: 221px;
        }

        &__link {
            display: inline-block;
            font-size: 0.75rem;
            line-height: 1rem;
            margin-top: 0.5rem;
        }
    }

    &__carousel {
        position: relative;
        width: 100%;
        user-select: none;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        margin-top: 4rem;

        @media (min-width: 1024px) {
            padding-left: 5rem;
            padding-right: 5rem;
            padding-bottom: 2.5rem;
        }

        .swiper-slide {
            position: relative;
            user-select: none;
        }



        .swiper-slide::before {
            display: none;

            @media (min-width: 768px) {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                left: -2rem;
                width: 1px;
                height: 8rem;
                background-color: #d1d5db;
            }
        }

        .swiper-slide-reset-before::before {
            content: none !important;
            display: none !important;
        }

        .swiper-button-prev,
        .swiper-button-next {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 0;
            cursor: pointer;
            width: 2.75rem;
            height: 2.75rem;
            user-select: none;
            border-radius: 9999px;
            color: white;
            background: linear-gradient(to right, var(--theme-color-brand-gradient-from), var(--theme-color-brand-gradient-to));
            z-index: 10;
        }

        @media (min-width: 1024px) {
            .swiper-button-prev,
            .swiper-button-next {
                top: 50%;
                margin-top: -3rem;
                transform: translateY(-50%);
            }
        }

        .swiper-button-prev.swiper-button-disabled,
        .swiper-button-next.swiper-button-disabled {
            background: none;
            background-color: #d1d5db;
            pointer-events: none;
            color: #374151;
        }

        .swiper-button-prev {
            left: 1.5rem;
        }

        @media (min-width: 1024px) {
            .swiper-button-prev {
                left: 0;
            }
        }

        .swiper-button-next {
            right: 1.5rem;
        }

        @media (min-width: 1024px) {
            .swiper-button-next {
                right: 0;
            }
        }

        .swiper-pagination {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            column-gap: 0.5rem;
            max-width: 24rem;
            margin-top: 1.5rem;
            margin-left: auto;
            margin-right: auto;

            @media (min-width: 768px) {
                margin-top: 2rem;
            }
            @media (min-width: 1280px) {
                margin-top: 3rem;
            }
        }



        .swiper-pagination-bullet {
            position: relative;
            flex: 1;
            width: auto;
            height: 0.25rem;
            border-radius: 0.75rem;
            overflow: hidden;
            opacity: 1;
            background-color: rgba(75, 85, 99, 0.2);
        }

        .swiper-pagination-bullet::before {
            content: "";
            position: absolute;
            inset: 0;
            transform-origin: left;
        }

        .swiper-pagination-bullet-active::before {
            background-color: #4b5563;
            animation: slide-progress 8s linear forwards;
        }

        &__slide {
            width: 100%;

            @media (min-width: 768px) {
                width: 50%;

            }

            @media (min-width: 1280px) {
                width: 33.333333%;
            }

            @media (min-width: 1536px) {
                width: 25%;
            }

            &__item {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;

                &__abbreviation {
                    width: 52px;
                    height: 52px;
                    background-color: var(--theme-color-base-white);
                    border: 2px solid var(--theme-color-brand);
                    border-radius: 9999px;
                    overflow: hidden;
                    padding: 3px;

                    &__value {
                        background-color: #f5e8e6;
                        color: var(--theme-color-text-black);
                        border-radius: 9999px;
                        font-weight: bold;
                        text-transform: uppercase;
                        text-align: center;
                        width: 42px;
                        height: 42px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }

                &__name {
                    margin-top: 0.625rem;
                    font-size: 17px;
                    font-weight: bold;
                    color: var(--theme-color-text-black);
                }

                &__trustpilot {
                    margin-top: 0.5rem;
                    width: 133px;
                    height: 25px;
                }

                &__comment {
                    margin-top: 1rem;
                    font-size: 0.875rem;
                    text-align: center;
                    text-wrap: balance;
                    color: var(--theme-color-text-black);
                }
            }
        }
    }
}

@keyframes slide-progress {
    0% {
        transform: scaleX(0);
    }
    100% {
        transform: scaleX(1);
    }
}
