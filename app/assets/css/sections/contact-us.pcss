.contact-us {
    position: relative;
    text-align: center;
    background-color: #f3f4f6;
    border-radius: 1.5rem;
    margin-top: -1.25rem;
    padding: 2.75rem 2.25rem 2.25rem;

    @media (min-width: 1024px) {
        margin-top: 0;
        background-color: var(--theme-color-base-white);
        padding-top: 3rem;
        padding-left: 0;
        padding-right: 0;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    &__agents {
        display: flex;
        margin-top: 1.5rem;
        margin-bottom: 1.5rem;
        justify-content: center;
        overflow: hidden;

        .element > *:not(:first-child) {
            margin-left: -0.25rem;
        }

        @media (min-width: 1024px) {
            margin-bottom: 2.75rem;
        }
    }

    &__description {
        font-size: 1.125rem;
        line-height: 1.75rem;

        @media (min-width: 1024px) {
            max-width: 64rem;
            margin-left: auto;
            margin-right: auto;
        }

        &__action {
            width: fit-content;
            margin-left: auto;
            margin-right: auto;
            margin-top: 1.5rem;

            @media (min-width: 1024px) {
                margin-top: 2.5rem;
            }
        }
    }
}
