.subscribe {
    background-color: var(--theme-color-base-white);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border-radius: 4rem;
    padding: 2rem 1.5rem;
    text-align: center;

    @media (min-width: 1024px) {
        padding-left: 2rem;
        padding-right: 2rem;
        text-align: left;
        display: flex;
        align-items: center;
        border-radius: 3.25rem;
    }

    @media (min-width: 1280px) {
        padding: 2.5rem 7rem 2.5rem 5rem;
    }

    &__icon-container {
        flex-shrink: 0;
        margin-bottom: 0.5rem;
        color: var(--theme-color-brand);

        @media (min-width: 1024px) {
            margin-bottom: 0;
        }
    }

    &__icon {
        width: 4rem;
        height: 4rem;
        margin-left: auto;
        margin-right: auto;

        @media (min-width: 1024px) {
            width: 6rem;
            height: 6rem;
        }

        @media (min-width: 1280px) {
            width: 8rem;
            height: 8rem;
        }
    }

    &__form {
        flex: 1 1 auto;

        @media (min-width: 1024px) {
            margin-left: 2rem;
        }

        @media (min-width: 1280px) {
            margin-left: 4rem;
        }

        &__title {
            font-size: 1.5rem;
            line-height: 2rem;
            color: var(--theme-color-text-black);
            font-weight: bold;
        }

        &__description {
            color: var(--theme-color-text-black);
            margin-top: 0.625rem;

            @media (min-width: 1024px) {
                font-size: 1.125rem;
                line-height: 1.75rem;
            }
        }

        &__container {
            margin-top: 1.5rem;

            @media (min-width: 1024px) {
                display: flex;
                align-items: center;
            }

            &__name {
                position: relative;
                flex-grow: 1;
            }

            &__email {
                position: relative;
                flex-grow: 1;
                margin-top: 0.375rem;

                @media (min-width: 1024px) {
                    margin-top: 0;
                    margin-left: 1.5rem;
                }
            }

            &__action {
                flex: none;
                margin-top: 3rem;
                width: fit-content;

                @media (min-width: 1024px) {
                    margin-top: 0;
                    margin-left: 2.25rem;
                }
            }
        }
    }
}
