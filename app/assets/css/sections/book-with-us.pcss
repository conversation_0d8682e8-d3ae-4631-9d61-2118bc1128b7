.book-with-us {
    background-color: #ffffff;
    padding: 2.25rem 1.75rem 4rem;
    border-top-left-radius: 1.5rem;
    border-top-right-radius: 1.5rem;
    overflow: hidden;

    @media (min-width: 1024px) {
        background-color: transparent;
        padding: 0;
        border-radius: 0;
        overflow: visible;
    }

    &__container {
        display: flex;
        flex-direction: column;
        gap: 2.5rem;
        margin-top: 3rem;

        @media (min-width: 768px) {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            row-gap: 2.5rem;
            column-gap: 2rem;
        }

        @media (min-width: 1024px) {
            grid-template-columns: repeat(2, 1fr);
            column-gap: 4rem;
        }

        @media (min-width: 1280px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media (min-width: 1536px) {
            column-gap: 8rem;
        }
    }

    &__item {
        display: flex;
        text-align: start;

        &__icon {
            flex-shrink: 0;
            background-color: #ffffff;
            border-radius: 9999px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;

            svg {
                width: 2rem;
                height: 2rem;
            }

            @media (min-width: 1024px) {
                width: 6rem;
                height: 6rem;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

                svg {
                    width: 3.25rem;
                    height: 3.25rem;
                }
            }
        }

        &__info {
            margin-left: 2rem;

            @media (min-width: 768px) {
                margin-left: 1rem;
            }

            @media (min-width: 1024px) {
                margin-left: 1.25rem;
                margin-top: 0.75rem;
            }
        }
    }
}
