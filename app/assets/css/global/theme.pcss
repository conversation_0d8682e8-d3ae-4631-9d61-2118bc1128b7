:root {
    /* ======== Colors ======== */

    --theme-color-base-white: #ffffff;
    --theme-color-base-black: #000000;

    --theme-color-text-base: #111E26;
    --theme-color-text-secondary: #71777d;

    --theme-color-success: #73b51a;
    --theme-color-warning: #D88D00;
    --theme-color-info: #4a94ec;
    --theme-color-danger: #e6574b;

    --theme-color-input-border: #D7D7D7;

    /**
     * Brand configs example
     * Should be replaced with the actual brand colors
     */
    --theme-color-brand: #971d5a;
    --theme-color-brand-gradient-from: #9D1D5A;
    --theme-color-brand-gradient-to: #590c32;
    --gradient-angle: to bottom right;
    --gradient: linear-gradient(var(--gradient-angle), var(--theme-color-brand-gradient-from), var(--theme-color-brand-gradient-to));
    --text-gradient: linear-gradient(
        to bottom left,
        var(--theme-color-brand-gradient-from),
        var(--theme-color-brand-gradient-to)
    );

    /* ======== Border radius ======== */

    --theme-border-radius-input: 0.75rem;
    --theme-border-radius-layout: 50px;


    /* ======== Welcome Section ======== */

    --theme-color-secondary-900: #061620FF;
    --theme-color-text-white: #E4E4E4;
    --theme-color-text-black: #333333;
}
