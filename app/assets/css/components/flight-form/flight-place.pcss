.flight-place {
    position: relative;
    width: 100%;
    height: 55px;

    &:first-child::before {
        content: '';
        display: block;
        position: absolute;
        bottom: 0;
        height: 1px;
        width: 100%;
        background-color: #eee;
        z-index: 11;
    }

    &--open {
        &:first-child::before {
            background-color: #b4b4b4;
        }
    }

    &--open-options {
        &::before {
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            height: 1px;
            width: 100%;
            background-color: #b4b4b4;
            z-index: 11;
        }
    }

    @media (min-width: 1280px) {
        &:first-child::before,
        &::before {
            display: none;
        }
    }

    &__wrapper {
        width: 100%;
        overflow: hidden;
        border-radius: 30px;
        transition: 0.2s padding cubic-bezier(0.65, 0.05, 0.36, 1), 0.2s margin cubic-bezier(0.65, 0.05, 0.36, 1);

        &--open {
            height: auto;
            background: #fff;
            position: absolute;
            z-index: 15;

            @media (min-width: 1280px) {
                margin: -1.5rem;
                padding: 1.5rem;
                width: 30rem;
                box-shadow: 0 14px 14px rgb(0 0 0 / 25%);
            }
        }
    }

    &__field {
        cursor: pointer;
        align-items: center;
        padding: 0.5rem;
        margin: 0;
        transition: 0.2s all cubic-bezier(0.65, 0.05, 0.36, 1);

        &--open {
            label {
                color: red;
                background: linear-gradient(242.97deg, #590c32 0%, #9d1d5a 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;

                &::before {
                    display: none;
                }
            }
        }

        @media (min-width: 1280px) {
            &::before {
                content: '';
                position: absolute;
                display: block;
                width: 1px;
                height: calc(100% - 1.25rem);
                top: 50%;
                right: 0;
                background-color: #e4e4e4;
                transform: translateY(-50%);
            }
        }

        &__wrapper {
            display: grid;
            grid-template-columns: 1.75rem 1fr;
            grid-gap: 0.5rem;
            position: relative;
            align-items: center;

            @media (min-width: 768px) {
                grid-template-columns: 2.25rem 1fr;
            }

            @media (min-width: 1280px) {
                &--open {
                    padding-bottom: 1.5rem;

                    &::before {
                        position: absolute;
                        content: '';
                        display: block;
                        width: 31rem;
                        height: 1px;
                        bottom: 0;
                        left: 0;
                        margin: 0 -2.5rem;
                        background-color: #b4b4b4;
                    }
                }
            }
        }

        &__icon {
            height: 1.75rem;
            width: 1.75rem;

            display: flex;
            justify-content: center;
            align-items: center;

            @media (min-width: 768px){
                height: 2.25rem;
                width: 2.25rem;
            }
        }

        &__options {
            width: calc(100% + 20px);
            margin: 1rem -10px 0;

            max-height: 18rem;
            overflow-y: scroll;
            -ms-scroll-chaining: none;
            overscroll-behavior: contain;
            -ms-overflow-style: none;
            scrollbar-width: none;
            &::-webkit-scrollbar {
                display: none;
            }

            &__item {
                border-radius: 9999px;
                cursor: pointer;
                display: flex;
                padding: 0.625rem;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;

                &:hover {
                    --tw-gradient-from: #590c32;
                    --tw-gradient-to: #9d1d5a;
                    background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
                    --tw-gradient-stops: var(--tw-gradient-from),
                    var(--tw-gradient-to, rgba(89, 12, 50, 0));

                    .flight-place__field__options__item__info__city, .flight-place__field__options__item__info__country, .flight-place__field__options__item__code {
                        --tw-text-opacity: 1;
                        color: rgba(244, 246, 247, var(--tw-text-opacity));
                    }
                }

                &__icon {
                    border-radius: 9999px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    overflow: hidden;
                    width: 100%;

                    &__container {
                        border-radius: 9999px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 36px;
                        width: 36px;
                        overflow: hidden;
                    }

                    &--airport {
                        --tw-bg-opacity: 1;
                        background-color: rgba(241, 241, 241, var(--tw-bg-opacity));
                        border-radius: 9999px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 36px;
                        width: 36px;
                    }
                }

                &__info {
                    display: flex;
                    flex-direction: column;
                    flex-grow: 1;
                    line-height: 1;
                    margin-left: 0.625rem;

                    &__city {
                        font-weight: 700;
                        font-size: 14px;
                        line-height: 1.25rem;
                    }

                    &__country {
                        font-size: 12px;
                        line-height: 1rem;
                        --tw-text-opacity: 1;
                        color: rgba(113, 119, 125, var(--tw-text-opacity));
                    }
                }

                &__code {
                    -webkit-background-clip: text;
                    background-clip: text;
                    background-image: linear-gradient(270deg, var(--tw-gradient-stops));
                    --tw-gradient-from: #590c32;
                    --tw-gradient-stops: var(--tw-gradient-from),
                    var(--tw-gradient-to, rgba(89, 12, 50, 0));
                    --tw-gradient-to: #9d1d5a;
                    font-weight: 700;
                    font-size: 14px;
                    line-height: 20px;
                    color: transparent;
                    text-transform: uppercase;
                }
            }
        }
    }

    &__input {
        background-color: transparent;
        cursor: pointer;
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        width: 100%;

        &:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
        }

        &__container {
            display: grid;
            grid-template-columns: 1fr;
        }

        &__label {
            cursor: pointer;
            font-weight: 700;
            font-size: 0.625rem;
            --tw-text-opacity: 1;
            color: rgba(180, 180, 180, 1);
            text-transform: uppercase;
        }
    }
}
