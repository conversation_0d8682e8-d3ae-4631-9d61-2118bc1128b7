.datepicker-field {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    @media (min-width: 1280px) {
        flex: 1;
        flex-direction: row;
        align-items: center;
    }

    &__wrapper {
        position: relative;
        width: 100%;
        background: white;
        border-radius: 30px;
        height: 59px;
    }

    &__popup {
        width: 100%;
        background: var(--theme-color-base-white);
        padding: 0.625rem;
        border-radius: 30px;
        top: 0;
        left: 0;

        @media (min-width: 1280px) {
            position: absolute;
        }

        &.--open {
            position: absolute;
            box-shadow: 0 14px 14px rgba(0, 0, 0, 0.25);
            z-index: 20;

            @media (min-width: 1280px) {
                left: -68%;
                top: 15%;
                width: 53.5rem;

                margin: -1.5rem;
                padding: 1.5rem;
                padding-bottom: calc(1.5rem + 34px);

                background: #ffffff;
                box-shadow: 0 14px 14px rgba(0, 0, 0, 0.25);
                border-radius: 30px;

                z-index: 20;

            }
        }


    }

    &__popup-header {
        &.--open {
            @media (min-width: 1280px) {
                background: #f4f6f7;
                border-radius: 30px 30px 0 0;
                margin: -1.5rem;
                margin-bottom: 0;

                &.--padded {
                    padding: 8px 1.5rem;
                }

                &:not(.--padded) {
                    padding: 0 1.5rem;
                }
            }
        }
    }

    &__popup-body {
        display: flex;
        justify-content: center;

        &__navigation-items {
            position: absolute;
            bottom: -20px;
            padding: 0.5rem;
            background: linear-gradient(242.97deg, var(--theme-color-brand-gradient-to) 0%, var(--theme-color-brand-gradient-from) 100%);
            color: #fff;
            border-radius: 99999px;
            cursor: pointer;
            z-index: 1;

            &.--prev {
                left: 0;
            }

            &.--next {
                right: 0;
            }

            &.--disabled {
                background: rgb(230, 232, 234);
                color: black;
                cursor: unset;
            }
        }

        &__day {
            background: transparent;
            width: 1rem;
            height: 1rem;
            display: flex;
            -webkit-box-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            align-items: center;
            border-radius: 50%;
            padding: 1rem;
            margin: 0 auto;
        }

        .dp__calendar {
            .dp__calendar {
                min-height: 260px;
            }
        }

        .dp__main {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        .dp__menu {
            border: none !important;
        }

        .dp__calendar_header_item {
            font-weight: normal;
            font-size: 12px;
            display: inline-block;
            line-height: 1.7rem;
            text-align: center;
            width: 3.0625rem;
            height: 33px;
            padding: 5px;
            color: rgb(180, 180, 180);
            margin: 0;
        }

        .dp__calendar_header_separator {
            background: rgb(244, 246, 247);

        }

        .dp__calendar {
            padding: 0 0.5rem;
        }

        .dp__calendar_next {
            margin-inline-start: 0 !important;
        }

        .dp__calendar_item {
            font-size: 12px;
            font-weight: 700;
            line-height: 33px;
            color: rgb(51, 51, 51);
            width: 3.0625rem;
            border-radius: 0px;
            margin: 0.5px 0px;
            transform-style: preserve-3d;
            outline: none;
            background: transparent;
        }

        .dp__cell_inner {
            background: transparent;
            display: flex;
            -webkit-box-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            align-items: center;
            border-radius: 50%;
            margin: 0 auto;
            width: 3.0625rem;
            padding: 0;
            border: unset;
            height: unset;
        }

        .dp__cell_disabled {
            cursor: unset;
        }

        .dp__date_hover:hover {
            .datepicker-field__popup-body__day {
                background: var(--text-gradient);
                color: var(--theme-color-base-white);
            }
        }

        .dp--past.dp__date_hover:hover {
            background: unset;
            color: #c0c4cc;

            .datepicker-field__popup-body__day {
                background: unset;
                color: #c0c4cc;
            }
        }

        .dp__date_hover.dp__date_hover_start {
            background: linear-gradient(to left, rgb(29, 15, 88), rgb(29, 15, 88) 50%, transparent 50%, transparent 100%);;
            border-top-right-radius: unset;
            border-bottom-right-radius: unset;
            width: 100%;
        }

        .dp__date_hover.dp__date_hover_end {
            background: linear-gradient(to right, rgb(29, 15, 88), rgb(29, 15, 88) 50%, transparent 50%, transparent 100%);
            border-top-right-radius: unset;
            border-bottom-right-radius: unset;
            width: 100%;
        }

        .dp__today {
            border: none;
        }

        .dp__cell_offset {
            display: none;
        }

        .dp__range_start {
            background: linear-gradient(to left, rgb(29, 15, 88), rgb(29, 15, 88) 50%, transparent 50%, transparent 100%);;
            border-top-right-radius: unset;
            border-bottom-right-radius: unset;
            width: 100%;

            .datepicker-field__popup-body__day {
                background: var(--text-gradient);
                color: var(--theme-color-base-white);
            }
        }

        .dp__range_end {
            background: linear-gradient(to right, rgb(29, 15, 88), rgb(29, 15, 88) 50%, transparent 50%, transparent 100%);
            border-top-left-radius: unset;
            border-bottom-left-radius: unset;
            width: 100%;

            .datepicker-field__popup-body__day {
                background: var(--text-gradient);
                color: var(--theme-color-base-white);
            }
        }

        .dp__active_date {
            width: 100%;

            .datepicker-field__popup-body__day {
                background: var(--text-gradient);
                color: var(--theme-color-base-white);
            }
        }

        .dp__range_between {
            background: rgb(29, 15, 88, 1);
            border: none;
            color: var(--theme-color-base-white);
            padding: 0;
            width: 100%;
            height: 100%;
            border-radius: unset;
        }

        .dp__range_between.dp__date_hover {
            background: linear-gradient(to left, rgb(29, 15, 88), rgb(29, 15, 88) 100%, transparent 100%);;
        }

        .dp__calendar_row {
            margin: 0;
        }
    }

    &__row {
        display: grid;
        align-items: center;
        gap: 0.5rem;

        &.--cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        &.--cols-4 {
            grid-template-columns: repeat(4, 1fr);
        }

        @media (min-width: 1280px) {
            &.--cols-2 {
                grid-template-columns: 1fr repeat(1, 175px);
            }

            &.--cols-4 {
                grid-template-columns: 1fr repeat(3, 175px);
            }
        }
    }

    &__item {
        cursor: pointer;
        display: grid;
        grid-template-columns: 1fr 2.25rem;
        align-items: center;
        position: relative;
        padding-left: 1rem;
        width: auto;

        &.--active {
            .datepicker-field__label {
                background: linear-gradient(242.97deg, #590c32 0%, #9d1d5a 100%);
                color: var(--theme-color-brand-gradient-to);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            @media (min-width: 1280px) {
                background: white;
                height: 5.625rem;
            }
        }

        &::after {
            content: '';
            position: absolute;
            right: -5px;
            top: 0;
            height: calc(100% + 20px);
            width: 1px;
            background-color: #eee;

            @media (min-width: 1280px) {
                top: 50%;
                transform: translateY(-50%);
                height: 100%;
                right: -10px;
                background-color: #e4e4e4;
            }
        }

        &.--hide-divider::after {
            content: none;
        }


        @media (min-width: 1280px) {
            grid-template-columns: 125px 2.25rem;
            padding-left: 10px;
        }
    }

    &__input {
        display: grid;
        grid-template-columns: 1fr;
    }

    &__label {
        cursor: pointer;
        font-weight: 700;
        font-size: 10px;
        color: rgba(180, 180, 180, 1);
        text-transform: uppercase;
    }

    &__input-box {
        color: var(--theme-color-base-black);

        &__text {
            font-weight: 700;
            font-size: 14px !important;
            color: gray;

            &.--active {
                color: black;
            }
        }
    }

    &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.25rem;
        height: 2.25rem;
        color: #b4b4b4;

        &.--active {
            color: var(--theme-color-brand);
        }

        &.--clear {
            width: 2.25rem;
            height: 2.25rem;
            background: rgb(180, 180, 180);
            -webkit-box-align: center;
            align-items: center;
            -webkit-box-pack: center;
            justify-content: center;
            display: flex;
            border-radius: 50%;
            color: white;
        }
    }
}

.dp__input {
    padding: 0 !important;
    border: none !important;
    line-height: unset !important;
    background-color: unset !important;

    color: var(--theme-color-base-black) !important;

    cursor: pointer;
    width: 100%;
    font-family: 'sbc-font';
    font-weight: 700;
    font-size: 14px !important;

    &:focus {
        outline: none;
    }

    &::placeholder {
        transition: 0.2s all cubic-bezier(0.65, 0.05, 0.36, 1);
        color: inherit;
    }
}

.checkbox-slider {
    display: flex;
    align-items: center;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 120%;
    color: #7a7b7b;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;

    & input {
        display: none;
    }

    &__track {
        position: relative;
        display: inline-block;
        margin-right: 0.5rem;
        width: 46px;
        height: 26px;
        background-color: #dcdcdc;
        border-radius: 23px;
        transition: all 0.3s linear;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            width: 42px;
            height: 22px;
            background-color: #dcdcdc;
            border-radius: 11px;
            transform: translate3d(2px, 2px, 0) scale3d(1, 1, 1);
            transition: all 0.25s linear;
        }

        &::after {
            content: '';
            position: absolute;
            left: 0;
            width: 22px;
            height: 22px;
            background-color: #fff;
            border-radius: 11px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
            transform: translate3d(2px, 2px, 0);
            transition: all 0.2s ease-in-out;
        }
    }

    &:active &__track::after {
        width: 28px;
        transform: translate3d(2px, 2px, 0);
    }

    & input:checked + &__track {
        background: linear-gradient(242.97deg, #590c32 0%, #9d1d5a 100%);

        &::before {
            transform: translate3d(18px, 2px, 0) scale3d(0, 0, 0);
        }

        &::after {
            transform: translate3d(22px, 2px, 0);
        }
    }

    &:active input:checked + &__track::after {
        transform: translate3d(16px, 2px, 0);
    }
}
