.flight-manager {
    background-color: transparent;
    position: relative;
    z-index: 20;
    width: 100%;

    &__container {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        width: 100%;
    }

    &__item {
        display: flex;
        gap: 0.625rem;
        width: 100%;
        position: relative;
        border-radius: 99999px;
        padding: 0.25rem;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        background-color: transparent;

        @media (min-width: 1280px) {
            flex-wrap: nowrap;
            background-color: var(--theme-color-base-white);
        }

        &__cities {
            width: 100%;
            display: grid;
            background: var(--theme-color-base-white);
            border-radius: 30px;
            position: relative;

            @media (min-width: 1280px)  {
                max-width: unset;
                grid-template-columns: repeat(2, 1fr);
                flex: 2;
            }

            &__switch-button {
                cursor: pointer;
                background: linear-gradient(242.97deg, #590C32 0%, #9D1D5A 100%);
                color: var(--theme-color-base-white);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 2.25rem;
                width: 2.25rem;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: calc(50% + 10px);

                @media (max-width: 1280px) {
                    position: absolute;
                    top: 50%;
                    right: 10px;
                    transform: translateY(-50%);
                    z-index: 11;
                }

                svg {
                    @media (min-width: 1280px) {
                        transform: rotate(90deg);
                    }
                }
            }
        }
    }

    &__search {
        flex: none;
        margin-top: 0;
        width: 100%;
        cursor: pointer;

        @media (min-width: 1280px) {
            flex: none;
            margin-top: 0;
            width: 60px;
        }

        &__button {
            --tw-gradient-to: #9d1d5a;
            --tw-gradient-from: #590c32;
            --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(89, 12, 50, 0));
            background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
            border-radius: 9999px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            height: 2.5rem;
            font-size: 0.75rem;
            line-height: 1rem;
            --tw-text-opacity: 1;
            color: rgba(244, 246, 247, 1);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            width: 100%;

            @media (min-width: 1280px) {
                height: 3.75rem;
                width: 3.75rem;
            }
        }
    }
}
