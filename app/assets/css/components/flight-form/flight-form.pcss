.flight-form {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    max-width: 1152px;

    @media (min-width: 768px) {
        max-width: 768px;
    }

    @media (min-width: 1280px) {
        max-width: 1152px;
    }

    &__layout {
        position: relative;
        z-index: 10;
        padding: 4rem 0.75rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;

        @media (min-width: 640px) {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        @media (min-width: 768px) {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        @media (min-width: 1024px) {
            padding-left: 2rem;
            padding-right: 2rem;
        }

        &__container {
            width: 100%;
            max-width: 95%;
            margin-left: auto;
            margin-right: auto;
            margin-top: 4rem;

            @media (min-width: 640px) {
                max-width: 90%;
            }

            @media (min-width: 768px) {
                max-width: 42rem;
            }

            @media (min-width: 1024px) {
                max-width: 48rem;
            }

            @media (min-width: 1280px) {
                max-width: 72rem;
            }
        }
    }
}
