.trip-class {
    width: 100%;
    margin-bottom: 2.5rem;

    @media (min-width: 768px) {
        max-width: 42rem;
    }

    @media (min-width: 1024px) {
        max-width: 48rem;
    }

    @media (min-width: 1280px) {
        max-width: 72rem;
    }

    &__header {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    &__subheading {
        text-transform: uppercase;
        color: white;
        font-size: 0.875rem;
        font-weight: 700;
        letter-spacing: 0.1em;
    }

    &__body {
        display: none;

        @media (min-width: 1024px) {
            display: flex;
            align-items: center;
            margin-top: 1rem;
        }
    }

    &__title {
        color: white;
        font-size: 2.25rem;
        font-weight: 700;
    }

    &__icon-svg {
        width: 1.5rem;
        height: 1.5rem;
        stroke: currentColor;
    }

    &__option-svg {
        width: 1rem;
        height: 1rem;
        stroke: currentColor;
    }

    &__selector {
        position: relative;
        height: 3.25rem;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 9999px;
        z-index: 40;
        margin-left: 0.75rem;
        margin-right: 0.75rem;

        &__wrapper {
            position: relative;
        }

        &__backdrop {
            position: fixed;
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 75ms;
            width: 100%;
            height: 100%;
            inset: 0;
            opacity: 1;
        }

        &__background {
            visibility: hidden;
            position: absolute;
            opacity: 0;
            inset: 0;
            height: 100%;
            padding-top: 0.875rem;
            box-sizing: content-box;
            border-radius: 1.5rem;
            transform: scaleY(0.75);
            transform-origin: top;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: transform 150ms cubic-bezier(0.34, 1.56, 0.64, 1),
            opacity 150ms cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform, opacity;
        }

        &__caption {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;
            line-height: 1;
            padding-left: 1.75rem;
            color: #ffffff;
            transition: color 75ms ease-in;
            will-change: color;
        }

        &__label {
            font-size: 2.25rem;
            font-weight: 700;
            margin-top: -0.25rem;
        }

        &__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3.25rem;
            height: 3.25rem;
            border-radius: 9999px;
            background-color: rgba(255, 255, 255, 0.1);
            margin-left: 1.5rem;
        }

        &__list {
            overflow: hidden;
            position: relative;
            border-bottom-left-radius: 2.5rem;
            border-bottom-right-radius: 2.5rem;
            max-height: 0;
        }

        &__options {
            padding-bottom: 0.625rem;
        }

        &__option {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 0.875rem;
            margin: 0 10px;
            padding: 0.625rem 1.75rem;
            opacity: 0;
            cursor: pointer;
            transform: translateY(0.75rem);
            transition: transform 350ms cubic-bezier(0.34, 1.56, 0.64, 1),
            opacity 350ms cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform, opacity;

            &-icon {
                transform: translateX(-100%);
                opacity: 0;
                transition: transform 300ms, opacity 300ms;
                will-change: transform, opacity;
                margin-right: 0.25rem;
            }

            &-label {
                transform: translateX(-0.875rem);
                transition: transform 300ms;
                will-change: transform;
                color: #4A94EC;
            }

            &:hover {
                background-color: #EDF5FE;
                transition: none;
                border-radius: 10px;

                .trip-class__selector__option-label {
                    transform: translateX(0);
                    color: #282F36;
                }

                .trip-class__selector__option-icon {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        }

        &--open {
            .trip-class__selector__background {
                visibility: visible;
                background-color: #ffffff;
                opacity: 1;
                transform: none;
            }

            .trip-class__selector__caption {
                color: #374151;
            }

            .trip-class__selector__list {
                max-height: 24rem;
            }

            .trip-class__selector__option {
                opacity: 1;
                transform: translateY(0);

                &:nth-child(1) { transition-delay: 50ms; }
                &:nth-child(2) { transition-delay: 100ms; }
                &:nth-child(3) { transition-delay: 150ms; }
                &:nth-child(4) { transition-delay: 200ms; }
                &:nth-child(5) { transition-delay: 250ms; }
            }
        }
    }
}
