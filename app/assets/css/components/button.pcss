.button {
    background-color: var(--theme-color-base-white);
    color: var(--theme-color-brand);
    cursor: pointer;
    padding: 9px 2rem;
    font-weight: bold;
    line-height: 18px;
    min-width: 64px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap;
    user-select: none;

    font-size: var(--btn-font-size);
    height: var(--btn-size);
    border-radius: var(--btn-radius);

    &[disabled], &--disabled {
        color: #fff;
        opacity: 0.3;
        box-shadow: none;
        background-color: #4E5159;
        cursor: default;
    }

    &--primary {
        background: linear-gradient(242.97deg, var(--theme-color-brand-gradient-to) 0%, var(--theme-color-brand-gradient-from) 100%);
        color: #fff;
    }

    &--xs {
        --square-size: var(--btn-size-xs);
        height: var(--btn-size-xs);
        font-size: var(--btn-font-size-xs);
        padding: var(--btn-padding-xs);
    }

    &--sm {
        --square-size: var(--btn-size-sm);
        height: var(--btn-size-sm);
        font-size: var(--btn-font-size-sm);
        padding: var(--btn-padding-sm);
    }

    &--md {
        --square-size: var(--btn-size-md);
        height: var(--btn-size-md);
        font-size: var(--btn-font-size-md);
        padding: var(--btn-padding-md);
    }

    &--lg {
        --square-size: var(--btn-size-lg);
        height: var(--btn-size-lg);
        font-size: var(--btn-font-size-lg);
        padding: var(--btn-padding-lg);
    }
}

