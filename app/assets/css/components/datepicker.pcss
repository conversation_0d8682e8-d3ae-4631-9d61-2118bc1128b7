.datepicker {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 11px 15px -7px rgba(0, 0, 0, 0.2), 0 24px 38px 3px rgba(0, 0, 0, 0.14), 0 9px 46px 8px rgba(0, 0, 0, 0.12);
    width: 310px;

    &__header {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        background-color: var(--theme-color-brand-gradient-to);
        padding: 2rem;
        color: #fff;

        font-size: 18px;
        font-weight: bold;
        line-height: 1.5;
        letter-spacing: 0.00735em;

        &__button {
            opacity: 0.5;
            cursor: pointer;

            &--active {
                opacity: 1;
            }

            &--year {
                font-size: 14px;
                font-weight: bold;
                line-height: 1.2;
                letter-spacing: 0.1em;
            }
        }

        &__year {

        }
    }

    &__calendar {
        padding: 0.25rem 1rem 1rem 1rem;
        position: relative;
        height: 328px;
    }

    &__year-selector {
        padding: .25rem;
        display: flex;
        flex-direction: column;
        height: 328px;
        overflow: auto;

        &__button {
            font-size: 14px;
            font-weight: bold;
            line-height: 1.2;
            letter-spacing: 0.1em;
            cursor: pointer;
            height: 40px;
            flex-shrink: 0;
            display: flex;
            outline: none;
            align-items: center;
            justify-content: center;
            border-radius: 999px;

            &:hover {
                background-color: rgba(0, 0, 0, 0.04);
            }

            &--active {
                background-color: rgba(0, 0, 0, 0.04);
                color: var(--theme-color-brand-gradient-to);
            }
        }
    }

    &__month-selector {
        padding: .25rem;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        align-items: center;
        gap: 2px;
        height: 328px;
        overflow: auto;

        &__button {
            font-size: 14px;
            font-weight: bold;
            line-height: 1.2;
            letter-spacing: 0.1em;
            cursor: pointer;
            height: 40px;
            flex-shrink: 0;
            display: flex;
            outline: none;
            align-items: center;
            justify-content: center;
            border-radius: 999px;

            &:not(.datepicker__month-selector__button--disabled):hover {
                background-color: rgba(0, 0, 0, 0.04);
            }

            &--active {
                background-color: rgba(0, 0, 0, 0.04);
                color: var(--theme-color-brand-gradient-to);
            }

            &--disabled {
                cursor: default;
                color: rgba(0, 0, 0, 0.38);
            }
        }
    }

    &__footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
        padding: 0.5rem;

        &__button {
            color: var(--theme-color-brand-gradient-to);
            padding: 6px 8px;
            font-size: 12px;
            min-width: 64px;
            box-sizing: border-box;
            transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            font-weight: bold;
            line-height: 1.2;
            border-radius: 55px;
            letter-spacing: -0.03rem;
            text-transform: uppercase;

            &:hover {
                background-color: rgba(89, 12, 50, 0.04);
            }
        }
    }
}

.vue-datepicker {
    position: relative;

    &__arrow {
        color: rgba(0, 0, 0, 0.54);
        padding: 12px;
        overflow: visible;
        font-size: 2.4rem;
        text-align: center;
        transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
        border-radius: 999px;
        user-select: none;
        z-index: 10;
        position: absolute;
        top: 0.25rem;
        left: 2px;

        &--right {
            right: 2px;
            left: auto;
        }

        svg {
            width: 1.5rem;
            height: 1.5rem;
        }

        &--disabled {
            color: rgba(0, 0, 0, 0.26);
            cursor: default;
        }

        &:not(.vue-datepicker__arrow--disabled):hover {
            background-color: rgba(0, 0, 0, 0.04);
        }
    }
}

.vue-calendar {
    border-radius: 4px;
    width: fit-content;

    &--clickable {
        .vue-calendar__date:not(.vue-calendar__date--disabled, .vue-calendar__date--hidden) {
            cursor: pointer;
        }
    }

    &__header {
        @applt font-body1;
        text-align: center;
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }

    &__body, &__headings {
        display: grid;
        grid-template-columns: repeat(7, 40px);
    }

    &__headings {
        margin-bottom: 1rem;
    }

    &__body {
        user-select: none;
    }

    &__heading {
        color: rgba(0, 0, 0, 0.38);
        text-align: center;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.5;
        letter-spacing: 0.03333em;
    }

    &__date {
        font-size: 15px;
        cursor: default;
        font-weight: bold;

        &__wrapper {
            width: 100%;
            padding: 0 2px;
        }

        &__inner {
            border-radius: 18px;
            width: 36px;
            height: 36px;
            line-height: 36px;
            text-align: center;
        }

        &--other {
            color: var(--theme-color-base-black);
        }

        &--disabled {
            color: rgba(0, 0, 0, 0.38);
        }

        &--hidden {
            font-size: 0 !important;
        }

        &.vue-calendar__date--focused {
            .vue-calendar__date__wrapper .vue-calendar__date__inner {
                background-color: rgba(0, 0, 0, 0.3);
            }
        }

        &:not(.vue-calendar__date--hidden, .vue-calendar__date--disabled) {
            &.vue-calendar__date--focused {
                .vue-calendar__date__wrapper .vue-calendar__date__inner {
                    background-color: rgba(0, 0, 0, 0.3);
                }
            }

            &.vue-calendar__date--selected,
            &.vue-calendar__date--range-start,
            &.vue-calendar__date--range-end {
                .vue-calendar__date__inner {
                    background: var(--theme-color-brand-gradient-to);
                    color: #fff;
                }
            }


            &:not(.vue-calendar__date--selected) {
                &.vue-calendar__date--hover {
                    .vue-calendar__date__inner {
                        background: rgba(0, 0, 0, 0.04);
                    }
                }
            }

            &.vue-calendar__date--range-start, &.vue-calendar__date--range-end {
                .vue-calendar__date__wrapper {
                    /*noinspection CssUnresolvedCustomProperty*/
                    background-image: linear-gradient(to right, var(--tw-gradient-from) 50%, var(--tw-gradient-to) 50%);
                }
            }

            &.vue-calendar__date--range-left {
                .vue-calendar__date__wrapper {
                    @apply from-[#EEEEEE];
                }
            }

            &.vue-calendar__date--range-right {
                .vue-calendar__date__wrapper {
                    @apply to-[#EEEEEE] from-white;
                }
            }
        }

        &--range:not(&--range-start, &--range-end, &--other) {
            .vue-calendar__date__wrapper {
                @apply bg-[#EEEEEE];
            }
        }
    }
}
