/** Default configuration file */
@import "@tmg/modals/css/config.css";

/** Base modal styles, like positioning, overlay */
@import "@tmg/modals/css/modal.css";

/** Set of modal placement styles (optional) */
@import "@tmg/modals/css/positions.css";

/** ========== Transitions */

/** Modal transitions base */
@import "@tmg/modals/css/transitions.css";
/** Import every used transition separately */
@import "@tmg/modals/css/transitions/fade-up.css";
@import "@tmg/modals/css/transitions/slide-left.css";
@import "@tmg/modals/css/transitions/scale-up.css";
@import "@tmg/modals/css/transitions/fade.css";



/** ========== App styles ========== */

:root {
    --modal-overlay-background: rgba(0, 0, 0, 0.1);
    --modal-z-index: 100;
}

.modal {
    overflow-y: scroll;
}

.modal--center {
    padding: 2rem;

    @media (max-width: 450px) {
        padding: 1rem;
    }
}

.modal--in-screen {
    & > .modal__wrapper {
        display: flex;
        flex-direction: column;
        margin: 4vh auto;
        overflow-y: auto;
    }

    .app-modal__content {
        height: 100%;
    }
}

@layer components {
    .app-modal {
        width: 100%;
        max-width: 500px;
        background-color: var(--theme-color-base-white);
        position: relative;
        border-radius: 40px;

        &__close {
            position: absolute;
            top: 1rem;
            right: 1rem;

            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--theme-color-info);
            background: rgba(74, 148, 236, 0.1);
            border-radius: 23px;
            cursor: pointer;
            width: 46px;
            height: 46px;
            transition: box-shadow 0.3s ease;

            &:hover {
                box-shadow: 0 3px 14px rgba(0, 0, 0, 0.15);
            }
        }

        .section__title {
            padding-left: 3rem;
            padding-right: 3rem;
        }
    }
}

.modal-transition--scale-up.modal-enter-active,
.modal-transition--scale-up.modal-leave-active {
    transition-duration: 0.2s;
}

.modal-transition--scale-up.modal-enter-active .modal__wrapper, .modal-transition--scale-up.modal-leave-active .modal__wrapper {
    transition-duration: 0.2s;
}

