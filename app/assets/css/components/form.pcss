.form-field {
    position: relative;
    width: 100%;
    min-width: 0;
    break-inside: avoid;

    &__label {
        display: block;
        /*font-weight: bold;*/
        font-size: 16px;
        /*margin-bottom: 0.25rem;*/
        color: var(--theme-color-text-black);
        /*text-transform: uppercase;*/
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        position: absolute;
        top: -5px;
        left: 0;
        z-index: 2;
        pointer-events: none;
        transition: transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
        transform-origin: top left;
        transform: translate(16px, 14px) scale(0.75);
        line-height: 1rem;
        max-width: calc(100% - 48px);
    }

    &__body {
        > .input-text, > div > .input-text {
            --input-offset-top: 0.75rem;
            padding-top: var(--input-offset-top);
            height: 56px;
        }

        > .input-date, > div > .input-date {
            --input-offset-top: 1rem;
        }

        > .input-phone, > div > .input-phone {
            --input-offset-top: 1rem;
            height: 62px;

            > input {
                padding-top: var(--input-offset-top);
            }

            > .input-select, > div > .input-select {
                .input-select__value {
                    padding-top: var(--input-offset-top);
                }

                height: 60px;
            }
        }

        > .input-select, > div > .input-select {
            height: 62px;
            --input-offset-top: 1rem;

            .input-select__value__text {
                padding-top: var(--input-offset-top);
            }
        }
    }

    &__body {
        input:not([type="checkbox"],[type="radio"]),
        textarea {
            width: 100%;
        }
    }

    &__required {
        color: var(--theme-color-danger);
    }

    &__error {
        color: var(--theme-color-danger);
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }

    &__help {
        color: var(--theme-color-text-secondary);
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }

    &--centered {
        width: fit-content;
        margin-left: auto;
        margin-right: auto;
    }
}

.form {
    display: grid;
    gap: 0.75rem;

    &__inline-input {
        display: flex;
        align-items: center;
        gap: .75rem;
        cursor: pointer;

        &--start-aligned {
            align-items: flex-start;

            input {
                margin-top: 1px;
            }
        }

        &:is(label) {
            user-select: none;
        }
    }

    &__group-label {
        text-align: center;
        padding: .625rem 0;
        background: #F4F6F7;
        border-radius: 10px;
        margin-bottom: 1rem;

        @apply font-body1;
    }
}

.form-error {
    color: #D88D00;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    font-size: 14px;

    @media (max-width: 767px) {
        padding: 10px;
        background: rgb(216 141 0 / 10%);
        border-radius: 10px;
    }
}
