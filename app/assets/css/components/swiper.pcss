/* Scrollbar */
.swiper {
    &.swiper-horizontal > .swiper-scrollbar, .swiper-scrollbar.swiper-scrollbar-horizontal {
        /* background-color: theme('colors.primary.lighter') !important; */
        background-color: #E9E9E914 !important;

        .swiper-scrollbar-drag {
            background: theme('colors.white') !important;
            cursor: grab;
        }
    }
}

/* The last visible Slider */
.swiper-slide {
    &.swiper-slide-visible {
        /*opacity: 0.25;*/

        &.swiper-slide-fully-visible {
            /*opacity: 1;*/
        }
    }
}

.swiper-pagination {
    width: 24rem !important;
    display: flex;
    gap: 6px;
    margin-top: 1.5rem;
    padding: 0 1.5rem;

    &-bullet {
        height: 4px;
        width: 100%;
        background: #71777D20;
        border-radius: 2px;
        min-width: 8px;

        &-active {
            &:after {
                content: '';
                display: block;
                height: 4px;
                /*noinspection CssUnresolvedCustomProperty*/
                width: var(--progress, 0px);
                will-change: width;
                background: #71777D;
                border-radius: 2px;
            }
        }
    }
}

/* Slider Navigation Arrows */
.swiper-navigation {
    display: inline-grid;
    position: absolute;
    top: -170px;
    right: 200px;
    gap: 24px;
    z-index: 10;
}

.swiper-button {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    /* background: theme('colors.primary.lighter'); */
    background: #3F5D71;
    box-shadow: -1px 1px 1px 0 rgba(255, 255, 255, 0.50) inset, 1px -1px 1px 0 rgba(255, 255, 255, 0.33) inset;
    border-radius: 50%;
    cursor: pointer;

    @apply flex-none;

    &:hover {
        background-color: #FFFFFF25;
        box-shadow: 1px -1px 1px 0 rgba(255, 255, 255, 0.33) inset, -1px 1px 1px 0 rgba(255, 255, 255, 0.50) inset;
    }

    &.swiper-button-disabled {
        pointer-events: none;
        opacity: .5;
    }
}

@media (max-width: 1920px) {
    .swiper-navigation {
        right: 30px;
    }
}

@media (max-width: 1024px) {
    .swiper-navigation {
        display: none;
    }
}
