.input-number {
    display: flex;
    align-items: center;
    column-gap: 20px;
    justify-content: center;

    &__iterator {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--theme-color-info);
        background: color(from var(--theme-color-info) srgb r g b / 0.1);
        border-radius: 23px;
        cursor: pointer;
        width: 46px;
        height: 46px;
        transition: box-shadow 0.3s ease;
        flex-shrink: 0;

        svg {
            color: var(--theme-color-info);
        }
    }

    &.\--hide-controls {
        &, input {
            -moz-appearance: textfield !important;

            &::-webkit-inner-spin-button,
            &::-webkit-outer-spin-button {
                -webkit-appearance: none !important;
                -moz-appearance: textfield !important;
                margin: 0;
                z-index: -99;
            }
        }
    }
}
