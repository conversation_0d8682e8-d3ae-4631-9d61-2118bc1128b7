@layer components {
    .input-switch {
        position: relative;
        cursor: pointer;
        flex-shrink: 0;

        @apply outline-none appearance-none;

        --tw-ring-color: var(--ring-color);
        --switch-lever-padding: 2px;

        width: calc(var(--switch-lever-size) * 2);
        height: calc(var(--switch-lever-size) + var(--switch-lever-padding) * 2);
        border-radius: 100rem;
        background: var(--color);

        @apply transition-colors duration-100;

        /* Default colors */
        --color: var(--theme-color-success);
        --color-not-checked: #4E515930;

        --ring-color: var(--theme-color-success);
        --ring-color-not-checked: #4E5159;

        &:disabled, &[readonly] {
            cursor: not-allowed;
            opacity: 0.5;
        }

        /* Lever */

        &:before {
            content: '';
            display: block;
            position: absolute;
            border-radius: 100rem;
            margin: var(--switch-lever-padding);
            background: white;

            width: var(--switch-lever-size);
            height: var(--switch-lever-size);

            @apply transition-transform duration-100;

            box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
        }

        &:checked {
            /* Lever */

            &::before {
                transform: translateX(calc(var(--switch-lever-size) - var(--switch-lever-padding) * 2));
            }
        }

        &:not(:checked) {
            background: var(--color-not-checked);
        }

        &:focus {
            /*@apply ring-[2px];*/

            &:not(:checked) {
                --tw-ring-color: var(--ring-color-not-checked);
            }
        }

        /* Sizes =========================== */

        &.\--xs {
            /* 16px */
            --switch-lever-size: 1rem;
        }

        &, &.\--small { /* Default */
            /* 20px */
            --switch-lever-size: 1.25rem;
        }

        &.\--normal {
            /* 24px */
            --switch-lever-size: 1.5rem;
        }

        &.\--large {
            /* 28px */
            --switch-lever-size: 1.75rem;
        }

        &.\--xl {
            /* 32px */
            --switch-lever-size: 2rem;
        }
    }
}
