@layer components {
    .input-radio {
        --size: 24px;

        -webkit-appearance: none;
        appearance: none;
        background-color: #FFFFFF;
        margin: 0;

        color: darkgrey;
        width: var(--size);
        height: var(--size);
        border: 1px solid lightgrey;
        border-radius: 50%;

        display: grid;
        place-content: center;
        flex-shrink: 0;

        cursor: pointer;

        &:before {
            content: "";
            width: 8px;
            height: 8px;
            border-radius: 50%;
            transform: scale(0);
            box-shadow: inset 1em 1em white;
        }

        &:checked {
            background-color: var(--theme-color-success);
            border-color: var(--theme-color-success);
        }

        &:checked::before {
            transform: scale(1);
        }

        &:focus {
            outline: 0.25em solid #73b51a50; /* currentColor */
        }
    }
}
