.input-date {
    /*padding: 0 1rem;*/
    /*height: 48px;*/

    /*font-size: 16px;*/
    /*line-height: 24px;*/
    /*letter-spacing: 0.25px;*/

    /*border: 1px solid var(--theme-color-input-border);*/
    /*border-radius: var(--theme-border-radius-input);*/
    /*background-color: var(--theme-color-base-white);*/
    /*width: 100%;*/
    /*color: var(--theme-color-text-secondary);*/

    /*&[data-focused="true"], &[data-has-value="true"], &:invalid {*/
    /*    color: var(--theme-color-text-base);*/
    /*}*/

    /*&::-webkit-datetime-edit-fields-wrapper {*/
    /*    text-transform: uppercase;*/
    /*}*/

    /*&::-webkit-calendar-picker-indicator {*/
    /*    display: none;*/
    /*}*/
}

.input-date {
    position: relative;
    display: inline-block;
    width: 100%;

    &__field {
        width: 100%;
    }

    &__ghost {
        position: absolute;
        opacity: 0;
        pointer-events: none;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.25px;
        white-space: pre;
        top: 0;
        left: 0;
    }

    &__placeholder {
        position: absolute;
        top: calc(var(--input-offset-top) + 13px);
        pointer-events: none;
        z-index: 1;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.25px;
        font-weight: bold;
    }

    &__datepicker-button {
        position: absolute;
        top: 50%;
        margin-top: -24px;
        right: 0;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 2;
        color: var(--theme-color-text-secondary);
    }
}
