.input-phone {
    border: 1px solid var(--theme-color-input-border);
    border-radius: var(--theme-border-radius-input);
    height: 48px;
    display: flex;
    justify-content: space-between;
    color: var(--theme-color-text-base);

    .\--has-error & {
        /*border-color: var(--theme-color-danger);*/
    }

    .input-select {
        flex-shrink: 0;
        border: 0;

        &__options {
            display: flex;
            flex-direction: column;
            overflow: auto;
            height: 300px;
            width: 500px;
            gap: 2px;
        }

        &__value {
            padding: 0;
            width: 100%;
            height: 100%;
            border-radius: var(--theme-border-radius-input);
            font-weight: bold;
        }
    }

    > input {
        padding-left: 1rem;
        margin-left: -12px;
        font-weight: bold;

        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus,
        &:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px white inset !important;
            box-shadow: 0 0 0 30px white inset !important;
        }

        /*noinspection CssInvalidPseudoSelector*/
        &:is(:-webkit-autofill, :autofill),
        &:is(:-webkit-autofill, :autofill):hover,
        &:is(:-webkit-autofill, :autofill):focus,
        &:is(:-webkit-autofill, :autofill):active {
            box-shadow: 0 0 0 30px white inset !important;
        }
    }

    > .input-select.dropdown--opened > .dropdown__content {
        > .input-select__options {
            @media (max-width: 640px) {
                width: calc(100vw - 48px);
            }
        }
    }
}

