.input-select {
    display: flex;
    cursor: pointer;
    transition: all 0.2s ease-in-out; /* Adjust timing and easing as needed */
    position: relative;
    z-index: 1;
    user-select: none;
    min-width: 0.25rem;
    border: 1px solid var(--theme-color-input-border);
    border-radius: var(--theme-border-radius-input);
    height: 48px;

    select {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
        opacity: 0;
        z-index: 2;
        pointer-events: none;
    }

    .\--has-error :not(.input-phone) > &, .\--has-error > & {
        /*border-color: var(--theme-color-danger-500);*/

        .input-select__value {
            /*border-color: var(--theme-color-danger-500);*/
        }
    }

    &.\--scrollable {
        .input-select__options {
            max-height: 300px;
            overflow-y: auto;
        }
    }

    :not(&--styleless) > &__value {
        display: flex;
        gap: 0.75rem;
        border-radius: var(--theme-border-radius-input);
        padding: 0 1rem;
        align-items: center;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        position: relative;
        z-index: 1;

        min-width: 0;
        width: 100%;
        color: #43343B;
        background: #fff;

        font-size: 0.9375rem;

        svg {
            color: var(--theme-color-info)
        }

        &__text {
            flex-grow: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &__title {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-weight: bold;
            }

            &__subtitle {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #4B5563;

                font-size: .9em;
            }

            &--placeholder {
                font-size: 14px;
                color: var(--theme-color-text-secondary);
                line-height: 24px;
                letter-spacing: 0.25px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }

    &__arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: none;
        pointer-events: none;
        margin-left: auto;

        svg {
            transition: all 0.2s ease-in-out; /* Adjust timing and easing as needed */
        }

        .dropdown--opened & svg {
            transform: rotate(180deg);
        }

        &--active {
            svg {
                color: #00B3FF;
            }
        }
    }

    &.dropdown {
        position: relative;
    }

    &__options {
        z-index: 20;

        [data-popper-placement="bottom-start"] & {
            margin-top: 0.375rem;
        }

        [data-popper-placement="top-start"] & {
            margin-bottom: 0.375rem; /* Adjust value as needed */
        }

        background-color: white;
        border-radius: var(--theme-border-radius-input);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
        padding: 0.375rem;
        margin-top: 0.125rem;
        margin-bottom: 0.125rem;
        overflow: auto;

        min-width: 100%;
        width: max-content;
        max-width: max(100%, 80vw);
        max-height: 300px;

        &__no-results {
            padding: 0.5rem;
        }

        .input-text {
            width: 100%;
        }

        [role=option] + [role=option] {
            margin-top: 2px;
        }
    }

    &__option {
        padding: 0.375rem 1rem;
        display: flex;
        align-items: center;
        cursor: pointer;
        border-radius: var(--theme-border-radius-input);

        border: 1px solid transparent;

        &:not(.input-select__option--group):hover {
            background-color: #F8F8F8;
            border-color: #EEEEEE;
        }

        &--focused {
            border-color: #EEEEEE;
        }

        &--selected {
            background-color: #F8F8F8;
            border-color: #EEEEEE;
            color: #00B3FF;
        }

        svg {
            color: #90A3A9;
            margin-left: 2rem;
        }

        &__text {
            overflow: hidden;

            &__title {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        &--group {
            cursor: default;
            padding-left: 1rem;

            .input-select__option__text {
                &__title {
                    font-weight: bold;
                }
            }
        }
    }
}

