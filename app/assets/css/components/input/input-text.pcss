.input-text {
    border: 1px solid var(--theme-color-input-border);
    border-radius: var(--theme-border-radius-input);
    padding: 0 1rem;
    height: 48px;
    font-size: 16px;
    font-weight: bold;
    color: var(--theme-color-text-base);
    background: #fff;

    .\--has-error &, &.\--has-error {
        border-color: var(--theme-color-danger);
    }

    &:disabled {
        /*background: #f5f9ff;*/
    }

    &.\--hide-controls {
        /* Firefox */
        -moz-appearance: textfield;

        /* Chrome, Safari, Edge, Opera */
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
    }
}

