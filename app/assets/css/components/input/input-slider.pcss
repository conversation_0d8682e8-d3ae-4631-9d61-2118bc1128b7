@layer components {
    .input-slider {
        width: 100%;
        padding: 0 0 22px 0;

        --size: 32px;
        --snap-line-height: 32px;
        --snap-line-height-small: 16px;

        &--with-snaps {
            padding-top: var(--snap-line-height);
        }

        &__path {
            padding: calc(var(--size) / 2) 0;
            cursor: default;
            touch-action: none;
            position: relative;

            &__fill {
                background: #E8E8E8;
                height: 8px;

                &__inner {
                    position: absolute;
                    background: rgba(0, 0, 0, 0.2);
                    height: 8px;
                    transition: 0.3s cubic-bezier(0.18, 0.89, 0.28, 1.07);

                    &:nth-child(even) {
                        background: rgba(0, 0, 0, 0.5);
                    }
                }
            }

            &__snaps {
                position: absolute;
                top: 1rem;
                left: 0;
                width: 100%;
                z-index: 1;
            }

            &__snap {
                width: 1px;
                background-color: #ccc;
                height: var(--snap-line-height);
                position: absolute;
                bottom: 100%;

                &:nth-child(even) {
                    height: var(--snap-line-height-small);
                }

                &:last-child {
                    margin-left: -1px;
                }

                &__amount {
                    position: absolute;
                    font-size: 12px;
                    font-weight: bold;
                    bottom: 100%;
                    line-height: 1;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }

        &__move-path {
            position: relative;
            margin: calc(var(--size) / 2 * -1) 0 0 0;
            z-index: 2;
        }

        &__handle {
            position: absolute;
            top: calc(-1 * var(--size) / 2 - 4px);
            transform: translateX(-50%);
            width: var(--size);
            height: var(--size);
            border-radius: var(--size);
            user-select: none;
            cursor: pointer;
            touch-action: none;
            background: white;
            box-shadow: rgba(0, 0, 0, 0.25) 0px 4px 10px;
            transition: left 0.3s cubic-bezier(0.18, 0.89, 0.28, 1.07);

            &:focus {
                @apply ring;
            }
        }
    }
}
