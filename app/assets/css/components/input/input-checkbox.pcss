@layer components {
    .input-checkbox {
        appearance: none;
        padding: 0;
        print-color-adjust: exact;
        display: inline-block;
        vertical-align: middle;
        background-origin: border-box;
        user-select: none;
        flex-shrink: 0;
        border-width: 2px;
        border-color: currentColor;
        color: #d1d1d1;

        background-color: #FFFFFF;

        height: 20px;
        width: 20px;

        --tw-ring-color: #d1d1d150;
        --unchecked-color: #d2d2d2;

        /**
         * States
         * ===========================
         */

        &:focus, &.__state--focus {
            @apply ring-[3px] outline-none;
        }

        &:checked {
            --tw-ring-color: #73b51a50;
            color: var(--theme-color-success);
            border-color: transparent;
            background-color: currentColor;
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-image: var(--icon);
        }

        &:not(:disabled, &[readonly]) {
            cursor: pointer;
        }

        &:disabled, &[readonly] {
            cursor: not-allowed;
        }

        &.\--rounded {
            border-radius: 999px;
        }

        .\--has-error & {
            border-color: theme("colors.red.500");
        }
    }

    .input-checkbox {
        @apply rounded;

        --icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Crect width='24' height='24' rx='4' fill='url(%23paint0_radial_711_7397)'/%3E%3Cpath d='M18 7L9.75 16L6 11.9091' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3CradialGradient id='paint0_radial_711_7397' cx='0' cy='0' r='1' gradientUnits='userSpaceOnUse' gradientTransform='translate(23.0512 1.12653) rotate(135.222) scale(32.4739 66.5143)'%3E%3Cstop stop-color='%2373b51a'/%3E%3Cstop offset='1' stop-color='%2373b51a'/%3E%3C/radialGradient%3E%3C/svg%3E");

        &:disabled {
            --icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Crect width='24' height='24' rx='4' fill='url(%23paint0_radial_711_7397)'/%3E%3Cpath d='M18 7L9.75 16L6 11.9091' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3CradialGradient id='paint0_radial_711_7397' cx='0' cy='0' r='1' gradientUnits='userSpaceOnUse' gradientTransform='translate(23.0512 1.12653) rotate(135.222) scale(32.4739 66.5143)'%3E%3Cstop stop-color='%23858585'/%3E%3Cstop offset='1' stop-color='%23c2c2c2'/%3E%3C/radialGradient%3E%3C/svg%3E");
        }
    }
}
