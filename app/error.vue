<template>
    <div class="error-page">
        <div class="error-page__header">
            <div class="error-page__number error-page__number__part">
                {{ error.statusCode }}
            </div>
            <h1 class="error-page__title">
                {{ error.statusCode === 404 ? 'This page doesn\'t exist' : 'Something went wrong' }}
            </h1>
        </div>

        <p v-if="shouldShowTrace" class="error-page__message">
            {{ error.message }}
        </p>

        <pre
            v-if="shouldShowTrace"
            class="border p-4 rounded text-left mt-8"
            v-html="error.stack"
        />
    </div>
</template>

<script setup lang="ts">
const props = defineProps<{
    error: {
        message: string,
        statusCode: number,
        stack?: string,
    }
}>()

const shouldShowTrace = useRuntimeConfig().public.debug

onMounted(() => {
    console.error(props.error)
})
</script>
