<template>
    <NuxtLoadingIndicator />

    <Suspense>
        <NuxtLayout>
            <NuxtPage />
        </NuxtLayout>

        <template #fallback>
            <LoadingTemplate />
        </template>
    </Suspense>

    <ModalRenderer />
</template>

<script setup lang="ts">
import { ModalRenderer, useModalService } from '@tmg/modals'
import LoadingTemplate from '~/LoadingTemplate.vue'

//

const router = useRouter()
const modals = useModalService()

watch(router.currentRoute, () => {
    modals.closeAllModals()
})
</script>
