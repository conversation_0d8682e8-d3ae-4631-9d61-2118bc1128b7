<template>
    <div
        class="vue-calendar"
        :class="{
            'vue-calendar--clickable': clickEvents,
            'vue-calendar--hide-other': hideOther,
        }"
        tabindex="0"
        :aria-label="headerText"
        @keydown="handleKeydown"
        @blur="handleBlur"
    >
        <slot name="header">
            <div class="vue-calendar__header">
                <span class="vue-calendar__header__month">{{ headerMonth }}</span>&nbsp;
                <span class="vue-calendar__header__year">{{ headerYear }}</span>
            </div>
        </slot>
        <div v-if="headings" class="vue-calendar__headings">
            <div
                v-for="(heading, $i) in headings"
                :key="$i"
                class="vue-calendar__heading"
            >
                {{ heading }}
            </div>
        </div>
        <div class="vue-calendar__body">
            <Day
                v-for="(date, $i) in dates"
                :key="$i"
                :date="date"
                :index="$i"
            />
        </div>
        <div class="vue-calendar__footer" />
    </div>
</template>

<script setup lang="ts">
import type { CalendarEmits, CalendarOptions } from '~/composables/useCalendar'

defineOptions({
    name: 'Calendar',
})

const props = withDefaults(defineProps<CalendarOptions>(), defaultOptions)

const emit = defineEmits<CalendarEmits>()

const {
    headerText, //
    headerMonth,
    headerYear,
    headings,
    dates,
    Day,
    handleKeydown,
    handleBlur,
} = useCalendar(props, emit)
</script>
