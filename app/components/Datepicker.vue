<template>
    <div class="datepicker">
        <div class="datepicker__header">
            <div
                class="datepicker__header__button datepicker__header__button--year"
                :class="{
                    'datepicker__header__button--active': mode === 'year',
                }"
                @click="changeMode('year')"
            >
                {{ headerYear }}
            </div>
            <div
                class="datepicker__header__button"
                :class="{
                    'datepicker__header__button--active': mode === 'day' || mode === 'month',
                }"
                @click="changeMode('month')"
            >
                {{ headerDateFormatted }}
            </div>
        </div>
        <div
            v-if="mode === 'year'"
            ref="yearsRef"
            class="datepicker__year-selector"
        >
            <button
                v-for="(year, $i) in years"
                :key="$i"
                type="button"
                class="datepicker__year-selector__button"
                :class="{
                    'datepicker__year-selector__button--active': year === headerYear,
                }"
                @click="selectYear(year)"
            >
                {{ year }}
            </button>
        </div>
        <div
            v-if="mode === 'month'"
            ref="monthsRef"
            class="datepicker__month-selector"
        >
            <button
                v-for="(month, $i) in months"
                :key="$i"
                type="button"
                class="datepicker__month-selector__button"
                :class="{
                    'datepicker__month-selector__button--active': month.label === headerMonth,
                    'datepicker__month-selector__button--disabled': !month.available
                }"
                :disabled="!month.available"
                @click="selectMonth(month.numeric)"
            >
                {{ month.label.slice(0,3) }}
            </button>
        </div>
        <div v-if="mode === 'day'" class="datepicker__calendar">
            <button
                aria-label="Previous months"
                type="button"
                class="vue-datepicker__arrow vue-datepicker__arrow--left"
                :class="{
                    'vue-datepicker__arrow--disabled': !canNavigateBackward,
                }"
                @click="navigateBackward"
            >
                <IconChevron class="rotate-90" />
            </button>

            <button
                aria-label="Next months"
                type="button"
                class="vue-datepicker__arrow vue-datepicker__arrow--right"
                :class="{
                    'vue-datepicker__arrow--disabled': !canNavigateForward,
                }"
                @click="navigateForward"
            >
                <IconChevron class="-rotate-90" />
            </button>

            <div
                class="vue-datepicker"
                :class="{
                    'vue-datepicker--range': isRangeMode,
                }"
                @keydown="handleKeysNavigation"
            >
                <div class="vue-datepicker__calendars">
                    <template v-for="(calendarDate, $i) in calendars" :key="$i">
                        <slot
                            name="calendar"
                            :calendar-component="Calendar"
                            :calendar-props="{
                                range,
                                selectedDate: modelValue,
                                displayDate: calendarDate,
                                hoverEvents: !mobile,
                                clickEvents: true,
                                ...calendarProps,
                                onDateClick: handleDateClick,
                                onDateHover: handleDateHover,
                            }"
                        >
                            <Calendar
                                ref="calendarElements"
                                :range="range"
                                :selected-date="modelValue"
                                :display-date="calendarDate"
                                :hover-events="!mobile"
                                click-events
                                v-bind="calendarProps"
                                @date-click="handleDateClick"
                                @date-hover="handleDateHover"
                            />
                        </slot>
                    </template>
                </div>
            </div>
        </div>
        <slot name="footer" />
    </div>
</template>

<script setup lang="ts">
import type { DatepickerEmits, DatepickerOptions } from '~/composables/useDatepicker'
import { Calendar } from '#components'
import { formatDateWithoutYear } from '~/helpers/DateHelper'
import dayjs from 'dayjs'

defineOptions({
    name: 'Datepicker',
})

const props = withDefaults(defineProps<DatepickerOptions>(), {
    navigation: true,
    track: true,
    valueMode: 'day',
})

const emit = defineEmits<DatepickerEmits>()

const {
    isRangeMode, //
    calendars,
    navigateForward,
    navigateBackward,
    canNavigateForward,
    canNavigateBackward,
    handleDateClick,
    handleDateHover,
    handleKeysNavigation,
} = useDatepicker(props, emit)

//

const headerDate = computed(() => {
    const date = Array.isArray(props.modelValue)
        ? props.modelValue[0]
        : props.modelValue

    return date || new Date()
})

const headerYear = computed(() => {
    return headerDate.value.getFullYear()
})

const headerDateFormatted = computed(() => {
    if (props.valueMode === 'day') {
        return formatDateWithoutYear(headerDate.value)
    }

    return (months.value.find(month => month.numeric === headerDate.value.getMonth()))!.label
})

const headerMonth = computed(() => {
    return (months.value.find(month => month.numeric === headerDate.value.getMonth()))!.label
})

//

const mode = ref<'day' | 'month' | 'year'>(props.valueMode)

function changeMode(newMode: 'day' | 'month' | 'year') {
    mode.value = newMode

    if (newMode === 'year') {
        nextTick(() => {
            const activeYear = yearsRef.value?.querySelector('.datepicker__year-selector__button--active') as HTMLButtonElement | null

            if (!activeYear) {
                return
            }

            yearsRef.value?.scrollTo({
                top: activeYear?.offsetTop - yearsRef.value?.offsetTop - yearsRef.value?.offsetHeight / 2,
            })
        })
    }
}

//

const years = computed(() => {
    let year = 1900

    const now = new Date()

    const maxYear = props.canNavigateForward
        ? (() => {
            for (let i = year; i < 2100; i++) {
                if (!props.canNavigateForward([dayjs(now).set('year', i).toDate()])) {
                    return i
                }
            }

            return 2100
        })()
        : 2100

    const minYear = props.canNavigateBackward
        ? (() => {
            for (let i = maxYear; i > 1900; i--) {
                if (!props.canNavigateBackward([dayjs(now).set('year', i).toDate()])) {
                    return i
                }
            }

            return 1900
        })()
        : 1900

    const years = []

    if (minYear) {
        year = minYear
    }

    while (year <= maxYear && year >= minYear) {
        years.push(year)
        year++
    }

    return years
})

const yearsRef = ref<HTMLDivElement>()

/**
 * @important Range mode is not supported
 */
function selectYear(year: number) {
    if (!props.modelValue) {
        emit('update:modelValue', dayjs().set('year', year).toDate())

        if (props.valueMode === 'day') {
            changeMode('day')
        }

        return
    }

    if (Array.isArray(props.modelValue)) {
        return
    }

    const newDate = dayjs(props.modelValue).set('year', year).toDate()

    emit('update:modelValue', newDate)

    if (props.valueMode === 'day') {
        changeMode('day')
    }
}

// month

const allMonths = [
    { numeric: 0, label: 'January' },
    { numeric: 1, label: 'February' },
    { numeric: 2, label: 'March' },
    { numeric: 3, label: 'April' },
    { numeric: 4, label: 'May' },
    { numeric: 5, label: 'June' },
    { numeric: 6, label: 'July' },
    { numeric: 7, label: 'August' },
    { numeric: 8, label: 'September' },
    { numeric: 9, label: 'October' },
    { numeric: 10, label: 'November' },
    { numeric: 11, label: 'December' },
]

const months = computed(() => {
    return allMonths.map(month => {
        const date = new Date()
        date.setUTCFullYear(headerYear.value, month.numeric, 15)

        // Iterate over every date in the month
        // If there is active date, then month is available

        let available = false

        for (let i = 1; i <= 31; i++) {
            date.setUTCDate(i)

            const isActive = props.calendarProps?.disabledDates
                ? !props.calendarProps?.disabledDates(dayjs(date))
                : true

            if (isActive) {
                available = true
                break
            }
        }

        return {
            ...month,
            available,
        }
    })
})

function selectMonth(month: number) {
    if (!props.modelValue) {
        emit('update:modelValue', dayjs().set('month', month).toDate())

        if (props.valueMode === 'day') {
            changeMode('day')
        } else if (props.valueMode === 'month') {
            changeMode('year')
        }

        return
    }

    if (Array.isArray(props.modelValue)) {
        return
    }

    const newDate = dayjs(props.modelValue).set('month', month).toDate()

    emit('update:modelValue', newDate)

    if (props.valueMode === 'day') {
        changeMode('day')
    } else if (props.valueMode === 'month') {
        changeMode('year')
    }
}
</script>
