<template>
    <div class="trip-type-selector">
        <button
            v-for="type in tripTypes"
            :key="type.id"
            class="trip-type-selector__button"
            :class="{ 'trip-type-selector__button--active': modelValue === type.id }"
            @click="emit('update:modelValue', type.id)"
        >
            {{ type.text }}
        </button>
    </div>
</template>

<script setup lang="ts">
import { TripType } from '@tmg/flight-form'

const props = defineProps<{
    modelValue: number,
}>()

const emit = defineEmits<{
    'update:modelValue': [value: TripType]
}>()

const tripTypes = [
    { id: TripType.Return, text: 'Round trip' },
    { id: TripType.OneWay, text: 'One way' },
    { id: TripType.MultiCity, text: 'Multi city' },
]
</script>

