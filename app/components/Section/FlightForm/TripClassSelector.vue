<template>
    <div class="trip-class">
        <div class="trip-class__header">
            <div class="trip-class__subheading">
                THINK COMFORT
            </div>

            <div class="trip-class__body">
                <span class="trip-class__title">Travel</span>
                <div v-if="isOpen" class="trip-class__selector__backdrop" />

                <div class="trip-class__selector" :class="{ 'trip-class__selector--open': isOpen }">
                    <div ref="dropdownRef" class="trip-class__selector__wrapper">
                        <div class="trip-class__selector__background" />
                        <button class="trip-class__selector__caption" @click="isOpen = !isOpen">
                            <span class="trip-class__selector__label">
                                {{ currentLabel }} class
                            </span>
                            <span class="trip-class__selector__icon">
                                <svg
                                    class="trip-class__icon-svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="3"
                                        d="M19 9l-7 7-7-7"
                                    />
                                </svg>
                            </span>
                        </button>

                        <div class="trip-class__selector__list">
                            <ul class="trip-class__selector__options">
                                <li
                                    v-for="(item, index) in tripClassItems"
                                    :key="index"
                                    class="trip-class__selector__option"
                                    @click="selectOption(item.value)"
                                >
                                    <span class="trip-class__selector__option-icon">
                                        <svg
                                            class="trip-class__option-svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M9 5l7 7-7 7"
                                            />
                                        </svg>
                                    </span>
                                    <span class="trip-class__selector__option-label">{{ item.label }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { TripClass } from '@tmg/flight-form'

const props = defineProps<{
    modelValue: string
}>()

const emit = defineEmits(['update:modelValue'])

const isOpen = ref(false)
const dropdownRef = ref(null)

onClickOutside(dropdownRef, () => {
    isOpen.value = false
})

const tripClassItems = [
    { value: 'premium', label: 'Premium class' },
    { value: TripClass.Business, label: 'Business class' },
    { value: TripClass.First, label: 'First class' },
]

const selectOption = (value) => {
    emit('update:modelValue', value)
    isOpen.value = false
}

const currentLabel = computed(() => {
    const selected = tripClassItems.find((item) => {
        return item.value === props.modelValue
    })

    return selected ? selected.label.split(' ')[0] : ''
})
</script>
