<template>
    <div class="datepicker-field">
        <div class="datepicker-field__wrapper">
            <div
                ref="popup"
                class="datepicker-field__popup"
                :class="{ '--open': isOpen }"
            >
                <div class="datepicker-field__popup-header" :class="{ '--padded': tripType === 2, '--open': isOpen }">
                    <div class="datepicker-field__row" :class="`--cols-${gridCols}`">
                        <div v-if="isOpen" />
                        <div
                            class="datepicker-field__item"
                            :class="{
                                '--active': isSelectingStart,
                                '--hide-divider': isOpen,
                            }"
                            @click="handleClick('start')"
                        >
                            <div class="datepicker-field__input">
                                <label class="datepicker-field__label">Departure</label>
                                <div class="datepicker-field__input-box">
                                    <span class="datepicker-field__input-box__text --active">
                                        {{
                                            selectedStartDate ? formatDate(selectedStartDate) : 'Select date'
                                        }}
                                    </span>
                                </div>
                            </div>

                            <div class="datepicker-field__icon" :class="{'--active': isSelectingStart}">
                                <IconFlightCalendar />
                            </div>
                        </div>
                        <div
                            class="datepicker-field__item"
                            :class="{
                                '--active': isSelectingEnd,
                                '--hide-divider': isOpen,
                            }"
                            @click="handleClick('end')"
                        >
                            <div class="datepicker-field__input">
                                <label class="datepicker-field__label">Return</label>
                                <div class="datepicker-field__input-box">
                                    <span
                                        class="datepicker-field__input-box__text"
                                        :class="{ '--active': tripType !== 1 }"
                                    >
                                        {{
                                            selectedEndDate ? formatDate(selectedEndDate) : '+ Add return'
                                        }}
                                    </span>
                                </div>
                            </div>

                            <div
                                v-if="!isOpen || (isOpen && !isSelectingEnd)"
                                class="datepicker-field__icon"
                                :class="{
                                    '--active': isSelectingEnd,
                                }"
                            >
                                <IconFlightCalendar />
                            </div>
                            <div
                                v-else
                                class="datepicker-field__icon --clear"
                                @click.stop="changeToOneWay"
                            >
                                <IconClear />
                            </div>
                        </div>
                        <div v-if="isOpen && tripType !== TripType.MultiCity">
                            <label class="checkbox-slider">
                                <input
                                    type="checkbox"
                                    :checked="tripType === TripType.OneWay"
                                    @change="switchTripType($event.target.checked)"
                                >
                                <i class="checkbox-slider__track" />
                                One way
                            </label>
                        </div>
                    </div>
                </div>
                <div v-if="isOpen" class="datepicker-field__popup-body">
                    <Datepicker
                        v-model="selectedDate"
                        :multi-calendars="2"
                        :inline="true"
                        :enable-time-picker="false"
                        :format="'d MMM yyyy'"
                        :min-date="minDate"
                        :max-date="maxDate"
                        :month-change-on-scroll="false"
                        auto-apply
                        :range="tripType !== 1"
                        :week-start="0"
                    >
                        <template #month-year="{ month, year, instance, handleMonthYearChange }">
                            <div class="custom-header-wrap w-full flex items-center justify-center my-5">
                                <button
                                    v-if="instance === 0"
                                    class="datepicker-field__popup-body__navigation-items --prev"
                                    :class="{
                                        '--disabled': isPrevDisabled(month, year)
                                    }"
                                    :disabled="isPrevDisabled(month, year)"
                                    @click="handleMonthYearChange(false)"
                                >
                                    <IconChevron class="rotate-90" />
                                </button>
                                <span class="font-bold text-[15px] text-black">{{ formatHeader(month, year) }}</span>
                                <button
                                    v-if="instance === 1"
                                    class="datepicker-field__popup-body__navigation-items --next"
                                    :class="{
                                        '--disabled': isNextDisabled(month, year)
                                    }"
                                    :disabled="isNextDisabled(month, year)"
                                    @click="handleMonthYearChange(true)"
                                >
                                    <IconChevron class="-rotate-90" />
                                </button>
                            </div>
                        </template>
                        <template #day="{ day }">
                            <span class="datepicker-field__popup-body__day">{{ day }}</span>
                        </template>
                    </Datepicker>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Datepicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'
import { onClickOutside } from '@vueuse/core'
import { TripType } from '@tmg/flight-form'

const { flightFormData } = useSearchFlightStore()

const tripType = computed(() => {
    return flightFormData.tripType
})

const isOpen = ref(false)
const isSelectingStart = ref(false)
const isSelectingEnd = ref(false)

const selectedDate = ref()
const selectedStartDate = computed(() => {
    if (!selectedDate.value) {
        return ''
    }

    return selectedDate.value?.[0]
})

const selectedEndDate = computed(() => {
    if (!selectedDate.value || !selectedDate.value?.[1]) {
        return ''
    }

    return selectedDate.value?.[1]
})

const handleClick = (type: 'start' | 'end') => {
    isOpen.value = true

    if (type === 'start') {
        isSelectingStart.value = true
        isSelectingEnd.value = false
    } else {
        isSelectingStart.value = false
        isSelectingEnd.value = true

        if (tripType.value === TripType.OneWay) {
            flightFormData.tripType = TripType.Return
        }
    }
}

const gridCols = computed(() => {
    return tripType.value === 2 ? 2 : 4
})

//

const popup = ref()

onClickOutside(popup, () => {
    isOpen.value = false
    isSelectingStart.value = false
    isSelectingEnd.value = false
})

//

function formatHeader(month: number, year: number) {
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December',
    ]

    return `${monthNames[month]} ${year}`
}

function formatDate(date: Date) {
    return new Intl.DateTimeFormat('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
    }).format(date)
}

//

const maxDate = computed(() => {
    const today = new Date()
    const futureDate = new Date(today)
    futureDate.setFullYear(today.getFullYear() + 1)

    return futureDate
})

const minDate = computed(() => {
    return new Date()
})

//

function isPrevDisabled(month: number, year: number): boolean {
    const current = new Date(year, month, 1)

    return current <= new Date(minDate.value)
}

function isNextDisabled(month: number, year: number): boolean {
    const current = new Date(year, month + 1, 0) // последняя дата месяца

    return current >= new Date(maxDate.value)
}

function changeToOneWay() {
    flightFormData.tripType = TripType.OneWay
    isSelectingStart.value = true
    isSelectingEnd.value = false
}

function switchTripType(isOneWay: boolean) {
    if (isOneWay) {
        changeToOneWay()
    } else {
        flightFormData.tripType = TripType.Return
    }
}
</script>
