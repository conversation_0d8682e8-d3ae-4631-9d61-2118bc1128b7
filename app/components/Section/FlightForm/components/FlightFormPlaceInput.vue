<template>
    <div
        ref="placeRef"
        class="flight-place"
        :class="{ 'flight-place--open': isOpen, 'flight-place--open-options': isOpen && isOptions }"
        @click="openSearch"
    >
        <div
            ref="placeSearchRef"
            class="flight-place__wrapper"
            :class="{
                'flight-place__wrapper--open': isOpen
            }"
        >
            <div class="flight-place__field" :class="{ 'flight-place__field--open': isOpen }">
                <div
                    class="flight-place__field__wrapper"
                    :class="{ 'flight-place__field__wrapper--open': isOpen && isOptions}"
                >
                    <div class="flight-place__field__icon">
                        <IconSearch v-if="isOpen" />
                        <IconPlaceCity v-else-if="isCity" />
                        <IconPlaceAirport v-else />
                    </div>
                    <div class="flight-place__input__container">
                        <label class="flight-place__input__label">{{ label }}</label>
                        <input
                            ref="inputRef"
                            v-model="searchQuery"
                            type="text"
                            class="flight-place__input"
                            :placeholder="placeholder"
                            @input="handleInputChange"
                            @keydown.esc="closeSearch"
                        >
                    </div>
                </div>
                <div v-if="isOptions" class="flight-place__field__options">
                    <div
                        v-for="(place, index) in fetchedPlaces"
                        :key="index"
                        class="flight-place__field__options__item group"
                        @click.stop="selectPlace(place)"
                    >
                        <div v-if="place.type === 'city'" class="flex w-full h-full">
                            <div class="flight-place__field__options__item__icon__container">
                                <div class="flight-place__field__options__item__icon">
                                    <IconPlaceCity />
                                </div>
                            </div>
                            <div class="flight-place__field__options__item__info">
                                <div class="flight-place__field__options__item__info__city">
                                    {{ place.name }}
                                </div>
                                <div class="flight-place__field__options__item__info__country">
                                    {{ place.country.name }}
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flight-place__field__options__item__code">
                                    {{ place.code }}
                                </div>
                                <IconChevron class="-rotate-90 w-6 h-6 stroke-gradient group-hover:text-white group-hover:stroke-current ml-1" />
                            </div>
                        </div>
                        <div v-else class="flex w-full h-full">
                            <div class="flight-place__field__options__item__icon__container">
                                <div class="flight-place__field__options__item__icon">
                                    <IconPlaceArrow class="h-4 stroke-current text-secondary-600 group-hover:text-white -mt-1" />
                                </div>
                            </div>
                            <div class="flight-place__field__options__item__icon__container">
                                <div class="flight-place__field__options__item__icon flight-place__field__options__item__icon--airport">
                                    <IconPlaceAirport class="w-4 fill-current text-secondary-600" />
                                </div>
                            </div>
                            <div class="flight-place__field__options__item__info">
                                <div class="flight-place__field__options__item__info__city">
                                    {{ place.name }}
                                </div>
                                <div class="flight-place__field__options__item__info__country">
                                    {{ place.country.name }}
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flight-place__field__options__item__code">
                                    {{ place.code }}
                                </div>
                                <IconChevron class="-rotate-90 w-6 h-6 stroke-gradient group-hover:text-white group-hover:stroke-current ml-1" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onClickOutside, useDebounceFn } from '@vueuse/core'
import type { Aero, PlaceIdentification } from '@tmg/aero-data-sdk'

const props = defineProps<{
    label: string,
    placeholder: string,
    modelValue: PlaceIdentification | undefined,
}>()

const emit = defineEmits<{
    'update:modelValue': [value: PlaceIdentification]
}>()

const aeroDataStore = useAeroDataStore()

const placeSearchRef = ref(null)

const isOpen = ref(false)

const isOptions = computed(() => {
    return fetchedPlaces.value.length > 0
})

const isCity = computed(() => {
    return lastSelectedOption.value?.type === 'city'
})

function openSearch() {
    if (isOpen.value) { return }
    isOpen.value = true
    nextTick(() => {
        inputRef.value?.focus()
    })
}

onClickOutside(placeSearchRef, () => {
    closeSearch()
})

//

const internalValue = computed({
    get: () => props.modelValue,
    set: (val: PlaceIdentification) => emit('update:modelValue', val),
})

const searchQuery = ref('')

const lastSelectedOption = ref<Aero.PlaceSuggestion>()

//

const fetchedPlaces = ref([])

const inputRef = ref<HTMLInputElement | null>(null)

const debouncedSearch = useDebounceFn(async (value: string) => {
    fetchedPlaces.value = await aeroDataStore.searchPlaces(value)
}, 300)

function handleInputChange(event: Event) {
    const target = event.target as HTMLInputElement
    debouncedSearch(target.value)
}

async function selectPlace(place: Aero.PlaceSuggestion) {
    lastSelectedOption.value = place
    internalValue.value = {
        code: place.code,
        type: place.type,
    }

    await aeroDataStore.fetchPlacesInfo([{
        code: place.code,
        type: place.type,
    }])

    searchQuery.value = `${place.name} (${place.code})`

    isOpen.value = false
    fetchedPlaces.value = []
}

function closeSearch() {
    isOpen.value = false
    fetchedPlaces.value = []

    if (lastSelectedOption.value) {
        searchQuery.value = `${lastSelectedOption.value.name} (${lastSelectedOption.value.code})`
    } else {
        searchQuery.value = ''
    }
}

watch(() => internalValue.value, (val) => {
    if (!val) {
        searchQuery.value = ''

        return
    }

    const place = aeroDataStore.getPlaceInfo(val)

    if (place) {
        searchQuery.value = `${place.name} (${place.code})`
        lastSelectedOption.value = place
    }
})
</script>
