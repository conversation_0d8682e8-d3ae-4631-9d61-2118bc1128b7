<template>
    <div class="flight-manager">
        <div class="flight-manager__container">
            <div
                v-for="(route, index) in routes"
                :key="index"
                class="flight-manager__item"
            >
                <div
                    class="flight-manager__item__cities"
                    :class="{
                        '!max-w-[496px]': index === 0 && selectedTripType !== TripType.MultiCity
                    }"
                >
                    <FlightFormPlaceInput
                        v-model="route.from"
                        label="From"
                        placeholder="Flying from?"
                    />
                    <div class="flight-manager__item__cities__switch-button" @click="switchItineraryRoutes(index)">
                        <IconPlaceSwitch class="w-5 h-5 md:w-6 md:h-6 fill-current text-grey-400" />
                    </div>
                    <FlightFormPlaceInput
                        v-model="route.to"
                        label="To"
                        placeholder="Where are you flying?"
                    />
                </div>
                <FlightFormDatepicker />
                <div class="text-right min-w-[190px]">
                    test
                </div>
                <div class="flight-manager__search" @click="search">
                    <div class="flight-manager__search__button">
                        <IconSearchFlight />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { TripType } from '@tmg/flight-form'
import FlightFormPlaceInput from '~/components/Section/FlightForm/components/FlightFormPlaceInput.vue'
import FlightFormDatepicker from '~/components/Section/FlightForm/components/FlightFormDatepicker.vue'

const { flightFormData } = useSearchFlightStore()

const selectedTripType = computed(() => {
    return flightFormData.tripType
})

const routes = computed(() => {
    return flightFormData.routes
})

function switchItineraryRoutes(index: number) {
    const from = flightFormData.routes[index].from
    const to = flightFormData.routes[index].to
    flightFormData.routes[index].from = to
    flightFormData.routes[index].to = from
}

//

function search() {
    console.log('search')
}
</script>
