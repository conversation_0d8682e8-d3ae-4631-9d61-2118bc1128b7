<template>
    <a :href="data.route" class="best-deals__item group">
        <div class="best-deals__item__card">
            <div class="best-deals__item__card__place">
                <AppPicture
                    :src="data.imageSrc"
                    alt="place"
                    :img-attrs="{ class: 'best-deals__item__card__place__image' }"
                />
                <div class="best-deals__item__card__place__shadow" />
            </div>
            <div class="best-deals__item__card__description">
                <div class="best-deals__item__card__description__container">
                    <div class="best-deals__item__card__description__container__info">
                        <p v-if="cardType !== BestDealType.Airline" class="best-deals__item__card__description__container__info__profit">
                            <span class="best-deals__item__card__description__container__info__profit__amount">Save up to {{ useFormatMoney(data.averageProfit) }}*</span> off
                            {{ data.class === BestDealClass.First ? 'first class' : 'premium flights' }}
                        </p>
                        <h2 class="best-deals__item__card__description__container__info__title">
                            {{ title }}
                        </h2>
                        <span v-if="cardType !== BestDealType.Airline" class="best-deals__item__card__description__container__info__price">
                            {{ useFormatMoney(data.cheapestPrice) }}  <sup class="best-deals__item__card__description__container__info__price__currency text-gradient">*USD</sup>
                        </span>
                    </div>
                    <div class="best-deals__item__card__description__container__view">
                        <IconChevron class="best-deals__item__card__description__container__view__icon" />
                    </div>
                </div>
            </div>
        </div>
    </a>
</template>

<script setup lang="ts">
import { BestDealClass, type BestDeals } from '~/stores/useBestDealsStore'

const props = defineProps<{
    cardType: BestDealType
    data: Partial<BestDeals>
}>()

const title = computed(() => {
    if (props.cardType !== BestDealType.Airline) {
        if (props.data.class === BestDealClass.First) {
            return `First class to ${props.data.destination}`
        } else {
            return `Business class flights to ${props.data.destination}`
        }
    } else {
        return props.data.destination
    }
})
</script>
