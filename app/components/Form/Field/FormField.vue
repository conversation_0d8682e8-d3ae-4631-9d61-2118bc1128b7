<template>
    <div
        class="form-field"
        :class="{
            '--has-error': hasError,
        }"
    >
        <label v-if="label" class="form-field__label">
            {{ label }}&nbsp;<span v-if="required" class="form-field__required">*</span>
        </label>

        <div class="form-field__body">
            <slot />
        </div>

        <div v-if="!hideErrorText && hasError" class="form-field__error form-error">
            <IconAlert /> {{ error }}
        </div>

        <div v-if="help" class="form-field__help">
            {{ help }}
        </div>
    </div>
</template>

<script setup lang="ts" generic="TForm extends Form">
import type { Form } from '@tmg/form'
import { FormField, type PropsPathSimple } from '@tmg/form'

defineOptions({
    name: 'FormField',
})

//

const props = withDefaults(defineProps<{
    form?: TForm,
    field?: PropsPathSimple<TForm['data']>,

    help?: string,
    label?: string,
    required?: boolean,
    hideErrorText?: boolean,
}>(), {
    hideErrorText: false,
})

const field = computed<FormField>(() => {
    const field = props.field
    const form = props.form

    if (field instanceof FormField) {
        return field
    }

    if (!form) {
        throw new Error('Form prop must be provided')
    }

    if (!field) {
        throw new Error('Field prop must be provided')
    }

    return new FormField(form, field)
})

const hasError = computed(() => field.value.hasError)

const error = computed(() => {
    if (!props.form || !field.value) {
        return
    }

    const errors = field.value.errors

    return errors[0]
})
</script>
