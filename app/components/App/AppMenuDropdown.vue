<template>
    <div ref="wrapper" class="relative">
        <span
            class="header__menu__item"
            @click="toggle"
        >
            {{ item.title }}
            <IconChevron class="header__menu__item__icon w-4 h-4 mt-1" :class="{ 'rotate-180': isOpen }" />
        </span>
        <Transition name="slide-fade-up">
            <div v-if="isOpen" class="header__menu__submenu">
                <NuxtLink
                    v-for="sub in item.items"
                    :key="sub.route"
                    class="header__menu__submenu__item"
                    :to="{ path: sub.route }"
                    target="_blank"
                >
                    {{ sub.title }}
                </NuxtLink>
            </div>
        </Transition>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onClickOutside } from '@vueuse/core'
import type { MenuItem } from '~/composables/useMenuItems'

const props = defineProps<{
    item: MenuItem
}>()

const isOpen = ref(false)
function toggle() {
    isOpen.value = !isOpen.value
}

const wrapper = ref<HTMLElement | null>(null)

onClickOutside(wrapper, () => {
    isOpen.value = false
})
</script>
