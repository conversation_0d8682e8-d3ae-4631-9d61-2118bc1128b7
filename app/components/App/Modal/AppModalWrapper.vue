<template>
    <div class="app-modal">
        <div class="app-modal__content">
            <slot />
        </div>

        <slot v-if="'footer' in $slots" name="footer" />

        <button
            v-if="closeButton"
            class="app-modal__close"
            type="button"
            @click="close"
        >
            <IconX />
        </button>
    </div>
</template>

<script setup lang="ts">
import { useCurrentModal, useModal } from '@tmg/modals'

defineOptions({
    name: 'AppModalWrapper',
})

withDefaults(defineProps<{
    closeButton?: boolean,
}>(), {
    closeButton: true,
})

const modal = useModal(useCurrentModal())

function close() {
    modal.close()
}
</script>
