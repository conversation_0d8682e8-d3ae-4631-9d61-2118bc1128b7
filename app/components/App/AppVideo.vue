<template>
    <video
        ref="video"
        muted
        preload="auto"
        loop
        playsinline
    >
        <source
            v-for="(source, $i) in sources"
            :key="$i"
            :src="source.path"
            :type="source.mime"
        >
    </video>
</template>

<script lang="ts">
import manifest from '~/assets/video.manifest.json'
</script>

<script lang="ts" setup>
import { useDebounceFn } from '@vueuse/core'

interface VideoProps {
    /**
     * Should be exactly as source file in video.manifest.json
     */
    src: string;
    /**
     * If number is passed, then video will play after a given delay in ms.
     */
    autoplay?: boolean | number;
}

const props = defineProps<VideoProps>()

const asset = computed(() => (manifest[props.src] || []).sort((a, b) => a.screen - b.screen))
const screens = computed(() => asset.value.map((asset) => asset.screen).filter((v, i, a) => a.indexOf(v) === i))

const screen = ref(screens.value.length ? screens.value[screens.value.length - 1] : null)

const sources = computed(() => {
    return asset.value.filter((source) => {
        return source.screen === screen.value
    })
})

const handleResize = () => {
    const width = window.outerWidth

    for (const s of screens.value) {
        if (width <= s) {
            screen.value = s
            break
        }
    }
}

const handleDebouncedResize = useDebounceFn(handleResize, 100)

const video = ref(null)

const play = () => {
    video.value?.load()
    video.value?.play()
}

const runAutoplay = () => {
    if (props.autoplay) {
        setTimeout(
            () => {
                play()
            },
            typeof props.autoplay === 'number' ? props.autoplay : 10,
        )
    }
}

// watch(screen, () => {
//     runAutoplay()
// })

onMounted(() => {
    if (screens.value.length > 1) {
        handleResize()

        window.addEventListener('resize', handleDebouncedResize)
    }

    runAutoplay()
})

onUnmounted(() => {
    window.removeEventListener('resize', handleDebouncedResize)
})

defineExpose({ play })
</script>
