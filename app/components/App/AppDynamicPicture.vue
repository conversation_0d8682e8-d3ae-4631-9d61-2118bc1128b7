<template>
    <picture v-if="image" v-bind="pictureAtts">
        <source
            v-for="source in sources"
            :key="source.mime"
            :srcset="source.src"
            :media="source.mime"
        >

        <img
            v-if="img"
            :src="img.src"
            :width="width"
            :height="height"
            v-bind="$attrs"
        >
    </picture>
</template>

<script setup lang="ts">
import type { Aero } from '@tmg/aero-data-sdk'

type Image = Aero.Image
type ImageSource = Aero.ImageSource
type ThumbnailName = Aero.AirlineImageThumbnail

defineOptions({
    inheritAttrs: false,
})

const props = withDefaults(defineProps<{
    image: Image | undefined | null,
    thumbnail: ThumbnailName | 'original' | string, //
    width: number | string,
    height: number | string,
    pictureAtts?: any,

    // If set, only these formats will be used. If there is no source with the format, the default strategy will be used.
    formats?: string[],
}>(), {
    pictureAtts: {},
    formats: undefined,
})

const canBeDefaultSource = ({ mime }: ImageSource) => {
    return mime === 'image/jpg' || mime === 'image/jpeg' || mime === 'image/png'
}

const isSvg = (source: ImageSource) => {
    return source.mime === 'image/svg+xml'
}

const thumbnailSources = computed<ImageSource[] | undefined>(() => {
    if (!props.image) {
        return undefined
    }

    if (props.thumbnail === 'original') {
        return [props.image.original]
    }

    if (!(props.thumbnail in props.image.thumbnail)) {
        return undefined
    }

    return props.image.thumbnail[props.thumbnail as ThumbnailName]
})

const img = computed(() => {
    return thumbnailSources.value?.find((source: ImageSource) => {
        if (hasSomeOfAvailableFormats.value && !isAvailableFormat(source)) {
            return false
        }

        if (isSvg(source)) {
            return true
        }

        return canBeDefaultSource(source)
    })
})

const sources = computed(() => {
    const result: ImageSource[] = []

    if (img.value && isSvg(img.value)) {
        // If the main image is an SVG, we don't need other sources
        return []
    }

    for (const source of thumbnailSources.value ?? []) {
        // SVG can't be used as a source
        if (isSvg(source)) {
            continue
        }

        // Skip source used for the main image
        if (img.value?.mime === source.mime) {
            continue
        }

        if (hasSomeOfAvailableFormats.value && !isAvailableFormat(source)) {
            continue
        }

        result.push(source)
    }

    return result
})

const hasSomeOfAvailableFormats = computed(() => {
    return thumbnailSources.value?.some(isAvailableFormat)
})

const isAvailableFormat = (source: ImageSource) => {
    return props.formats && props.formats.includes(source.mime)
}
</script>
