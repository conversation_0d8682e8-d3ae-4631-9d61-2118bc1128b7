<template>
    <div
        class="dropdown"
        :class="{
            'dropdown--opened': isActive,
        }"
    >
        <slot
            name="toggle"
            :is-active="isActive"
            :close="close"
            :open="open"
            :toggle="toggle"
        />

        <ClientOnly>
            <Teleport
                v-if="shouldRenderContent"
                :to="teleportPosition"
                :disabled="!teleportPosition"
            >
                <Transition
                    v-if="withOverlay"
                    appear
                    name="fade"
                >
                    <div v-if="isActive" class="fixed z-40 inset-0 bg-black/50 pointer-events-none" />
                </Transition>
                <div
                    ref="contentRef"
                    class="dropdown__content z-50"
                    tabindex="-1"
                    :class="[
                        containerClass,
                        {
                            'w-full': full,
                        },
                    ]"
                >
                    <Transition
                        :name="transition"
                        appear
                    >
                        <slot
                            v-if="isActive"
                            name="content"
                            :is-active="isActive"
                            :close="close"
                            :open="open"
                            :toggle="toggle"
                        />
                    </Transition>
                </div>
            </Teleport>
        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import { createPopper, type Placement, type Options } from '@popperjs/core'
import { type MaybeElementRef, onClickOutside, useCurrentElement } from '@vueuse/core'

defineOptions({
    name: 'AppDropdown',
})

const props = withDefaults(defineProps<{
    opened?: boolean | undefined,
    full?: boolean,
    placement?: Placement,
    transition?: string,
    closeOnClickOutside?: boolean,
    ignoreClickOnElements?: MaybeRefOrGetter<MaybeElementRef[]>,
    unstoppableClose?: boolean,
    teleport?: boolean | string,
    popperOptions?: Partial<Options> | null,
    withOverlay?: boolean,
    containerClass?: string | string[] | Record<string, boolean>
}>(), {
    opened: undefined,
    full: false,
    placement: 'bottom-start',
    transition: 'slide-fade',
    closeOnClickOutside: true,
    ignoreClickOnElements: () => [],
    unstoppableClose: true,
    teleport: false,
    popperOptions: null,
    withOverlay: false,
    containerClass: undefined,
})

const emit = defineEmits(['open', 'close', 'toggle', 'click-outside', 'update:opened'])

const _isActive = ref(false)

const isActive = computed({
    get: () => props.opened ?? _isActive.value,
    set: (value) => {
        if (props.opened === undefined) {
            _isActive.value = value
        }

        emit('update:opened', value)
    },
})

const contentRef = ref(null)

let popperInstance: any
let timeout: any

const el = useCurrentElement()

watch(isActive, () => {
    if (!isActive.value) {
        timeout = setTimeout(() => {
            if (popperInstance) {
                popperInstance.destroy()
            }
        }, 250)

        return
    }

    const toggle = (el.value as any).children[0] as HTMLElement

    if (!toggle) {
        return
    }

    const content = contentRef.value

    if (!content) {
        return
    }

    if (popperInstance) {
        popperInstance.destroy()
    }

    if (timeout) {
        clearTimeout(timeout)
    }

    initPopper(toggle, content)

    popperInstance.forceUpdate()
}, { immediate: true })

onMounted(() => {
    if (props.closeOnClickOutside) {
        onClickOutside(el.value as HTMLElement, handleClickOutside, {
            ignore: () => {
                return [
                    contentRef,
                    ...toValue(props.ignoreClickOnElements),
                ]
            },
            capture: props.unstoppableClose,
        })
    }
})

onBeforeUnmount(() => {
    if (popperInstance) {
        popperInstance.destroy()
    }
})

const initPopper = (reference: any, popper: any) => {
    popperInstance = createPopper(reference, popper, Object.assign({
        placement: props.placement,
    }, props.popperOptions))
}

const handleClickOutside = (event: any = null) => {
    close(event)
    emit('click-outside', event)
}

const close = (event: any = null) => {
    if (!props.unstoppableClose && event && event.eventPhase === 0) {
        return
    }

    if (!isActive.value) {
        return
    }

    isActive.value = false

    emit('close')
    emit('toggle', isActive.value)
}

const open = () => {
    isActive.value = true

    emit('open')
    emit('toggle', isActive.value)
}

const toggle = () => {
    if (isActive.value) {
        close()
    } else {
        open()
    }
}

const teleportPosition = computed(() => {
    if (typeof props.teleport === 'boolean') {
        return props.teleport ? 'body' : null
    }

    return props.teleport
})

const shouldRenderContent = computed(() => 'content' in useSlots())

//

defineExpose({
    open,
    close,
    toggle,
    isActive,
})
</script>
