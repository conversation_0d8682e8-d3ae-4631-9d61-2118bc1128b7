<template>
    <picture>
        <source
            v-for="({ type, srcset }, $i) in sources"
            :key="$i"
            :type="type"
            :srcset="srcset.srcset"
            :sizes="srcset.sizes"
        >
        <NuxtImg
            :src="src"
            :width="width"
            :height="height"
            :sizes="sizes"
            :quality="quality"
            :modifiers="modifiers"
            v-bind="imgAttrs"
            :loading="loading"
        />
    </picture>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
    src: string
    sizes?: string[]
    modifiers?: any,
    quality?: number,
    width?: number | string,
    height?: number | string,
    minHeight?: number | string,
    imgAttrs?: { [key: string]: any },
    loading?: 'eager' | 'lazy'
}>(), {
    sizes: undefined,
    modifiers: null,
    quality: undefined,
    width: undefined,
    height: undefined,
    minHeight: undefined,
    imgAttrs: undefined,
    loading: undefined,
})

const formats = ['webp']

const $img = useImage()

const screens = useAppConfig().layout.breakpoints

const width = computed(() => props.width ? Number(props.width) : undefined)
const height = computed(() => props.height ? Number(props.height) : undefined)
const minHeight = computed(() => props.minHeight ? Number(props.minHeight) : undefined)

const sizes = computed(() => {
    if (props.sizes) {
        return props.sizes
    }

    const orderedScreens = Object.entries(screens).sort(([, sizeA], [, sizeB]) => sizeA - sizeB)
    const availableScreens: string[] = []

    for (const [, size] of orderedScreens) {
        availableScreens.push(`${size}px`)

        if (width.value && size > width.value) {
            availableScreens.push(`${width.value}px`)
            break
        }
    }

    return availableScreens //@todo - fix needed because (max-width: 2px) 768px, 576px at img tag
})

const sources = computed(() => {
    return formats.map((format) => {
        const srcset = $img.getSizes(props.src, {
            sizes: sizes.value,
            modifiers: Object.fromEntries(
                Object.entries({
                    ...props.modifiers,
                    quality: props.quality,
                    width: width.value,
                    height: height.value,
                    format,
                }).filter(([, value]) => value !== undefined),
            ),
        },
        )

        if (minHeight.value) {
            srcset.srcset = srcset.srcset.replace(/x(\d+)/g, (replacement: string, height: string) => {
                return 'x' + Math.max(parseInt(height), minHeight.value ?? 0)
            })

            srcset.src = srcset.src.replace(/x(\d+)/g, (replacement: string, height: string) => {
                return 'x' + Math.max(parseInt(height), minHeight.value ?? 0)
            })

            srcset.srcset = srcset.srcset.replace(/w_(\d+)/g, 's_$1x' + minHeight.value)
            srcset.src = srcset.src.replace(/w_(\d+)/g, 's_$1x' + minHeight.value)
        }

        return {
            type: 'image/' + format,
            srcset,
        }
    })
})
</script>
