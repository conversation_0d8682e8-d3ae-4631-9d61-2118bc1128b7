<template>
    <div class="p-6 bg-gray-50 rounded-lg">
        <h2 class="text-2xl font-bold mb-4">Analytics Integration Example</h2>
        
        <!-- GDPR Status -->
        <div class="mb-6 p-4 bg-white rounded border">
            <h3 class="text-lg font-semibold mb-2">GDPR Status</h3>
            <div class="space-y-2 text-sm">
                <p><strong>European User:</strong> {{ isEuropeanUser ? 'Yes' : 'No' }}</p>
                <p><strong>Consent Given:</strong> {{ isAccepted ? 'Yes' : 'No' }}</p>
                <p><strong>Show Banner:</strong> {{ showBanner ? 'Yes' : 'No' }}</p>
                <p><strong>Can Load Analytics:</strong> {{ canLoadAnalytics ? 'Yes' : 'No' }}</p>
            </div>
            <div class="mt-3 space-x-2">
                <button 
                    @click="acceptConsent" 
                    class="px-3 py-1 bg-green-600 text-white rounded text-sm"
                >
                    Accept
                </button>
                <button 
                    @click="rejectConsent" 
                    class="px-3 py-1 bg-red-600 text-white rounded text-sm"
                >
                    Reject
                </button>
                <button 
                    @click="resetGdpr" 
                    class="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                >
                    Reset
                </button>
            </div>
        </div>

        <!-- UTM Data -->
        <div class="mb-6 p-4 bg-white rounded border">
            <h3 class="text-lg font-semibold mb-2">UTM Tracking</h3>
            <div class="space-y-2 text-sm">
                <p><strong>Has UTM Data:</strong> {{ hasUtmParameters ? 'Yes' : 'No' }}</p>
                <p><strong>Is Google Ads:</strong> {{ isGoogleAds ? 'Yes' : 'No' }}</p>
                <div v-if="hasUtmParameters" class="mt-2">
                    <p><strong>UTM Parameters:</strong></p>
                    <pre class="bg-gray-100 p-2 rounded text-xs">{{ JSON.stringify(utmData, null, 2) }}</pre>
                </div>
            </div>
            <div class="mt-3">
                <button 
                    @click="clearUtmData" 
                    class="px-3 py-1 bg-orange-600 text-white rounded text-sm"
                >
                    Clear UTM
                </button>
            </div>
        </div>

        <!-- Analytics Actions -->
        <div class="mb-6 p-4 bg-white rounded border">
            <h3 class="text-lg font-semibold mb-2">Analytics Actions</h3>
            <div class="space-y-2">
                <button 
                    @click="trackSampleProductDetail" 
                    class="block w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    :disabled="!canLoadAnalytics"
                >
                    Track Product Detail (JFK → LHR)
                </button>
                <button 
                    @click="trackSampleAddToCart" 
                    class="block w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                    :disabled="!canLoadAnalytics"
                >
                    Track Add to Cart
                </button>
                <button 
                    @click="trackSampleProductClick" 
                    class="block w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
                    :disabled="!canLoadAnalytics"
                >
                    Track Product Click
                </button>
                <button 
                    @click="sendCustomEvent" 
                    class="block w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                    :disabled="!canLoadAnalytics"
                >
                    Send Custom Event
                </button>
            </div>
        </div>

        <!-- Analytics State -->
        <div class="p-4 bg-white rounded border">
            <h3 class="text-lg font-semibold mb-2">Analytics State</h3>
            <div class="space-y-2 text-sm">
                <p><strong>GTM Loaded:</strong> {{ isGtmLoaded ? 'Yes' : 'No' }}</p>
                <p><strong>GA Client ID:</strong> {{ gaClientId || 'Not available' }}</p>
                <p><strong>Device ID:</strong> {{ deviceId || 'Not available' }}</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// GDPR Composable
const { 
    isEuropeanUser, 
    isAccepted, 
    showBanner, 
    canLoadAnalytics,
    acceptConsent, 
    rejectConsent, 
    resetGdpr 
} = useGdprCompliance()

// UTM Tracking Composable
const { 
    utmData, 
    isGoogleAds, 
    hasUtmParameters,
    clearUtmData 
} = useUtmTracking()

// Google Analytics Composable
const { 
    isGtmLoaded,
    getGaClientId,
    getDeviceId,
    sendEvent
} = useGoogleAnalytics()

// Ecommerce Composable
const { 
    trackProductDetail,
    trackAddToCart,
    trackProductClick
} = useEcommerce()

// Reactive client IDs
const gaClientId = ref<string | null>(null)
const deviceId = ref<string | null>(null)

// Update client IDs on mount
onMounted(() => {
    gaClientId.value = getGaClientId()
    deviceId.value = getDeviceId()
})

// Sample tracking functions
const trackSampleProductDetail = async () => {
    await trackProductDetail(
        'JFK - LHR',
        'JFK2LHR',
        6000.66,
        'roundTrip',
        'premium'
    )
}

const trackSampleAddToCart = async () => {
    await trackAddToCart(
        'JFK - LHR',
        'JFK2LHR',
        6000.66,
        'roundTrip',
        'premium',
        1
    )
}

const trackSampleProductClick = async () => {
    await trackProductClick(
        'JFK - LHR',
        'JFK2LHR',
        6000.66,
        'roundTrip',
        'premium'
    )
}

const sendCustomEvent = () => {
    sendEvent({
        event: 'custom_event',
        event_category: 'Test',
        event_action: 'Button Click',
        event_label: 'Analytics Example',
    })
}
</script>
