<template>
    <Teleport to="body">
        <Transition name="gdpr-banner">
            <div
                v-if="showBanner"
                class="fixed bottom-0 left-0 right-0 z-50 bg-gray-900 text-white p-4 shadow-lg"
            >
                <div class="container mx-auto max-w-6xl">
                    <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2">
                                <PERSON><PERSON>
                            </h3>
                            <p class="text-sm text-gray-300 leading-relaxed">
                                We use cookies and similar technologies to enhance your browsing experience, 
                                analyze website traffic, and provide personalized content. By clicking "Accept All", 
                                you consent to our use of cookies. You can manage your preferences or learn more 
                                in our 
                                <a href="/privacy-policy" class="text-blue-400 hover:text-blue-300 underline">
                                    Privacy Policy
                                </a>.
                            </p>
                        </div>
                        
                        <div class="flex flex-col sm:flex-row gap-3 min-w-fit">
                            <button
                                @click="rejectConsent"
                                class="px-4 py-2 text-sm border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 rounded transition-colors"
                            >
                                Reject All
                            </button>
                            <button
                                @click="acceptConsent"
                                class="px-6 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors font-medium"
                            >
                                Accept All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script setup lang="ts">
const { 
    showBanner, 
    acceptConsent, 
    rejectConsent,
    isLoading 
} = useGdprCompliance()

// Don't show banner while loading
const shouldShowBanner = computed(() => {
    return showBanner.value && !isLoading.value
})

// Use computed property for reactivity
const showBanner = shouldShowBanner
</script>

<style scoped>
.gdpr-banner-enter-active,
.gdpr-banner-leave-active {
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.gdpr-banner-enter-from {
    transform: translateY(100%);
    opacity: 0;
}

.gdpr-banner-leave-to {
    transform: translateY(100%);
    opacity: 0;
}

.gdpr-banner-enter-to,
.gdpr-banner-leave-from {
    transform: translateY(0);
    opacity: 1;
}
</style>
