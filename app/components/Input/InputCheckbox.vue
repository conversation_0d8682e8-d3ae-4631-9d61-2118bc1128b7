<template>
    <input
        type="checkbox"
        :name="name"
        class="input-checkbox"
        :required="required"
        :disabled="disabled || readonly"
        :readonly="readonly"
        :checked="checked"
        @change="handleUpdate"
        @input="onInput"
    >
</template>

<script setup lang="ts">
import { useCurrentElement } from '@vueuse/core'

defineOptions({
    name: 'InputCheckbox',
})

const props = withDefaults(defineProps<{
    name?: string,
    modelValue?: boolean,
    disabled?: boolean,
    readonly?: boolean,
    required?: boolean,
}>(), {
    name: undefined,
    modelValue: undefined,
    disabled: false,
    readonly: false,
    required: false,
})

const emit = defineEmits<{
    'update:modelValue': [value: boolean],
}>()

const checked = computed(() => Boolean(props.modelValue))

const el = useCurrentElement<HTMLInputElement>()

const isUnbinded = props.modelValue === undefined

function onInput(event: Event) {
    if (!isUnbinded) {
        el.value.checked = !(event.target as HTMLInputElement).checked
    }
}

function handleUpdate() {
    if (props.disabled || props.readonly) {
        return
    }

    let value: boolean

    if (isUnbinded) {
        value = el.value.checked
    } else {
        value = !checked.value
    }

    emit('update:modelValue', value)
}
</script>
