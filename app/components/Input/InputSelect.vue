<template>
    <AppDropdown
        ref="dropdown"
        class="input-select"
        :class="{
            'input-select--small': small,
            'input-select--large': large,
            'input-select--search': isSearch && searchPlacement === 'inToggle',
            'input-select--disabled': disabled,
        }"
        :popper-options="{
            ...popperOptions,
            modifiers: [
                sameWidthPopperModifier,
                ...popperOptions?.modifiers || [],
            ],
        }"
        :ignore-click-on-elements="ignoreClickOnElements"
        :teleport="teleport"
        @click-outside="handleClickOutside"
    >
        <template #toggle>
            <select tabindex="-1" @change="selectByIndex(Number(($event.target as HTMLSelectElement).value))">
                <option v-if="!withEmpty && placeholder?.length" value="-1">
                    -- Select {{ placeholder?.length ? placeholder.toLowerCase() : 'option' }}
                </option>

                <template v-if="optionsHasGroup">
                    <optgroup
                        v-for="(options, group) in groupedOptionsToRender"
                        :key="group"
                        :label="String(group)"
                    >
                        <option
                            v-for="option in options"
                            :key="option.value"
                            :value="option.value"
                        >
                            {{ option.title }}
                        </option>
                    </optgroup>
                </template>

                <template
                    v-for="(option, $i) in optionsToRender"
                    v-else
                    :key="$i"
                >
                    <option
                        :value="$i"
                        :selected="isSelected($i)"
                    >
                        <slot
                            name="mobile-option"
                            :option="option"
                            :index="$i"
                            :options-to-render="optionsToRender"
                            :has-group="hasGroup"
                        >
                            {{ option.title }}
                        </slot>
                    </option>
                </template>
            </select>

            <div
                v-if="!(isSearch && searchPlacement === 'inToggle')"
                class="input-select__value"
                tabindex="0"
                @mousedown="toggle"
                @focus="onInputFocus"
                @blur="onInputBlur"
                @keydown="onKeydown"
                @keyup="onKeyup"
            >
                <slot
                    name="result"
                    v-bind="{
                        option: selectedOption,
                        options: selectedOptions,
                    }"
                >
                    <template v-if="selectedOption && selectedOptions.length <= 1">
                        <slot
                            name="result-text"
                            v-bind="{
                                option: selectedOption,
                                options: selectedOptions,
                            }"
                        >
                            <img
                                v-if="selectedOption.image"
                                :src="selectedOption.image"
                                :alt="selectedOption.title"
                                class="input-select__image"
                            >

                            <div class="input-select__value__text" data-has-value="true">
                                <div class="input-select__value__text__title">
                                    {{ selectedOption.title }}
                                </div>
                            </div>
                        </slot>
                    </template>
                    <template v-else-if="selectedOptions.length > 1">
                        <slot
                            name="result-text"
                            v-bind="{
                                option: selectedOption,
                                options: selectedOptions,
                            }"
                        >
                            <div class="input-select__value__text">
                                <div class="input-select__value__text__title">
                                    {{ selectedOptionsResult }}
                                </div>
                            </div>
                        </slot>
                    </template>
                </slot>
                <div v-if="!noArrow" class="input-select__arrow">
                    <IconChevron />
                </div>
            </div>

            <slot
                v-if="isSearch && searchPlacement === 'inToggle'"
                name="input"
                v-bind="{ query, value, option: selectedOption }"
            >
                <input
                    ref="input"
                    type="text"
                    class="input-select__input input-select__value"
                    :class="{
                        'pr-10': !noArrow
                    }"
                    autocomplete="off"
                    :value="query"
                    :disabled="disabled"
                    @input="updateQuery"
                    @mousedown="toggle"
                    @focus="onInputFocus"
                    @blur="onInputBlur"
                    @keydown="onKeydown"
                    @keydown.tab="onTab"
                    @keyup="onKeyup($event, false)"
                >

                <div
                    v-if="clearButton && query.length && !freeInput"
                    class="input-select__clear"
                    @click="onClearClick"
                >
                    X
                </div>
                <div v-else-if="!noArrow" class="input-select__arrow absolute top-[22px] right-5 z-10">
                    <IconChevron />
                </div>
            </slot>
        </template>

        <template v-if="renderOptions" #content>
            <div
                v-if="!(isSearch && searchPlacement === 'inToggle' && !optionsToRender.length && freeInput)"
                ref="optionsContainerRef"
                class="input-select__options fancy-scroll overscroll-none"
                role="listbox"
            >
                <slot
                    v-if="isSearch && searchPlacement === 'inContent'"
                    name="searchInContent"
                >
                    <div class="sticky top-0 -mx-1.5 z-10 p-1.5 -translate-y-1.5 bg-white">
                        <InputText
                            :value="query"
                            type="text"
                            name="country"
                            :placeholder="'Type to search …'"
                            :disabled="disabled"
                            @input="updateQuery"
                            @keydown="onKeydown"
                            @keyup="onKeyup($event, false)"
                        />
                    </div>
                </slot>
                <slot
                    name="options"
                    :options="optionsToRender"
                    :is-focused="isFocused"
                >
                    <div
                        v-for="(option, $i) in optionsToRender"
                        :key="$i"
                        role="option"
                    >
                        <slot
                            name="option"
                            :index="$i"
                            :option="option"
                            :focused="isFocused($i)"
                            :selected="isSelectedValue(option.value)"
                            :select="($event: MouseEvent | KeyboardEvent) => select(option, Number($i), $event.shiftKey)"
                            :options-to-render="optionsToRender"
                            :has-group="hasGroup"
                        >
                            <div
                                v-if="hasGroup(option) && (!optionsToRender[$i-1] || optionsToRender[$i-1]?.group !== option.group)"
                                class="input-select__option input-select__option--group"
                            >
                                <div class="input-select__option__text">
                                    <div class="input-select__option__text__title">
                                        {{ option.group }}
                                    </div>
                                </div>
                            </div>
                            <div
                                class="input-select__option"
                                :data-index="$i"
                                :class="[{
                                    'input-select__option--selected': isSelected($i),
                                    'input-select__option--focused': isFocused($i),
                                    'input-select__option--in-group': hasGroup(option),
                                }, option.classes]"
                                @click="select(option, Number($i), $event.shiftKey)"
                            >
                                <img
                                    v-if="option?.image"
                                    :src="option.image"
                                    :alt="option.title"
                                    class="input-select__option__image"
                                >

                                <div class="input-select__option__text">
                                    <div class="input-select__option__text__title">
                                        {{ option.title }}
                                    </div>
                                </div>
                            </div>
                        </slot>
                    </div>
                    <div v-if="isSearch && !optionsToRender.length" class="input-select__options__no-results">
                        <template v-if="search && !query.length">
                            Start typing ...
                        </template>
                        <template v-else>
                            {{ noResultsText }}
                        </template>
                    </div>
                </slot>
            </div>
        </template>
    </AppDropdown>
</template>

<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import type { SelectOption } from '~/types/SelectOption'
import type { Modifier, Options } from '@popperjs/core'
import { groupBy } from '~/helpers/ArrayHelper'
import { useKeyboardListNavigation } from '~/composables/useKeyboardListNavigation'

const props = withDefaults(defineProps<{
    modelValue: any,
    options: SelectOption[],
    withEmpty?: boolean | string | SelectOption,
    teleport?: boolean | string,
    small?: boolean,
    large?: boolean,
    clearButton?: boolean,
    freeInput?: boolean, // Allow entered value to be emitted as is.
    limit?: number | false,
    disabled?: boolean,
    multiple?: boolean,
    autocomplete?: string,
    autocompleteSearch?: boolean,
    renderOptions?: boolean,
    optionsContainer?: HTMLElement | null,
    autofocus?: boolean,
    noArrow?: boolean,
    popperOptions?: Partial<Options>,
    focusableOptionsLength?: number,
    placeholder?: string,

    // Search props
    search?: boolean,
    filter?: boolean,
    loading?: boolean,
    debounce?: boolean | number,
    noResultsText?: string,
    searchPlacement?: 'inContent' | 'inToggle'
}>(), {
    teleport: true,
    withEmpty: false,
    small: false,
    large: false,
    clearButton: false,
    freeInput: false,
    limit: false,
    disabled: false,
    multiple: false,
    autocomplete: undefined,
    autocompleteSearch: false,
    renderOptions: true,
    optionsContainer: null,
    autofocus: false,
    noArrow: false,
    popperOptions: undefined,
    focusableOptionsLength: undefined,

    // Search props
    search: false,
    filter: false,
    loading: false,
    debounce: false,
    placeholder: undefined,
    noResultsText: 'No results',
    searchPlacement: 'inToggle',
})

const emit = defineEmits([
    'update:modelValue', //
    'update:option',
    'change',
    'open',
    'close',
    'focus',
    'blur',
    'focusMove',
    'search',
    'clear',
    'keydown',
])

// Refs declaration
// ================

const dropdown = ref<any>(null)
const input = ref<any>(null)
const optionsContainerRef = ref<any>(null)

const getOptionsContainer = (): HTMLElement => {
    return props.optionsContainer ?? optionsContainerRef.value
}

const ignoreClickOnElements = () => [
    input,
    getOptionsContainer(),
]

// Opened state manipulation
// ================

const isActive = computed(() => dropdown.value?.isActive)

const open = () => {
    if (!dropdown.value || props.disabled) {
        return
    }

    if (!isActive.value) {
        dropdown.value.open()

        emit('open')

        nextTick(() => {
            const container = getOptionsContainer()
            const selected = container.querySelector('.input-select__option--selected') as HTMLElement | null

            if (selected) {
                scrollTo(selected)
            }
        })
    }
}

const scrollTo = (element: HTMLElement) => {
    const container = getOptionsContainer()

    container.scrollTop = element.offsetTop - container.clientHeight / 2
}

const close = () => {
    if (!dropdown.value) {
        return
    }

    if (isActive.value) {
        dropdown.value.close()

        emit('close')
    }
}

const toggle = () => {
    if (dropdown.value?.isActive) {
        close()
    } else {
        open()
    }
}

// Handle options
// ================

const value = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
        emit('change', value)
    },
})

const selectedIndex = ref<number | number[] | null>(null)

const getIndexOfSelectedValue = (): number => {
    if (props.multiple) {
        return 0
    }

    if (selectedIndex.value !== null) {
        // @ts-ignore
        return selectedIndex.value
    }

    return options.value.findIndex(option => option.value === value.value)
}

const selectedOption = computed<SelectOption | null>(() => {
    return props.options.find((option) => option.value === value.value) ?? null
})

const selectedOptions = computed<SelectOption[]>(() => {
    if (!Array.isArray(value.value)) {
        return []
    }

    return options.value.filter(option => value.value.includes(option.value))
})

const selectedOptionsResult = computed(() => {
    if (!selectedOptions.value.length) {
        return ''
    }

    const result = selectedOptions.value.map(option => option.title).join(', ')

    return (selectedOptions.value.length > 1 ? `(${selectedOptions.value.length}) ` : '') + result
})

const emptyOptionValue: SelectOption = {
    title: '--------',
    value: null,
}

const options = computed(() => {
    return props.options
})

const optionsToRender = computed(() => {
    let options = props.options

    if (props.filter && !props.search) {
        const searchQuery = String(query.value).toLowerCase()

        if (searchQuery.length >= 1) {
            options = options.filter((option) => {
                return String(option.title).toLowerCase().includes(searchQuery)
                    || String(option.value).toLowerCase().includes(searchQuery)
            })
        }
    }

    if (props.withEmpty) {
        let empty = emptyOptionValue

        if (typeof props.withEmpty === 'object') {
            empty = props.withEmpty
        } else if (typeof props.withEmpty === 'string') {
            empty.title = props.withEmpty
        }

        options = [
            empty,
            ...options,
        ]
    }

    if (sort.value) {
        options = options.sort((a, b) => (a.sort || 0) - (b.sort || 0))
    }

    return options
})

const optionsHasGroup = computed(() => {
    return optionsToRender.value.filter((option) => option.group).length !== 0
})

const groupedOptionsToRender = computed(() => {
    return groupBy(optionsToRender.value, 'group')
})

watch(value, (value) => {
    if (props.multiple && Array.isArray(value) && !selectedOptions.value.every(option => value.includes(option.value))) {
        selectedIndex.value = null
    } else if (value !== selectedOption.value?.value) {
        selectedIndex.value = null
    }
})

// Group
const group = computed(() => props.options.some(hasGroup))
const hasGroup = (option: SelectOption) => 'group' in option && option.group !== null && option.group !== ''

// Sort
const sort = computed(() => props.options.some(option => 'sort' in option))

//

const selectValue = (newValue: any, append = false) => {
    if (props.multiple) {
        if (newValue === null || newValue === undefined) {
            newValue = append ? value.value : null
        } else if (Array.isArray(value.value) && append) {
            if (isSelectedValue(newValue)) {
                newValue = value.value.filter(v => v !== newValue)
            } else {
                newValue = [...value.value, newValue]
            }
        } else {
            newValue = [newValue]
        }
    }

    value.value = newValue
}

const selectValueIndex = (index: number | null, append = false) => {
    if (props.withEmpty && append && index === 0) {
        return
    }

    let result: number | number[] | null = index

    if (props.multiple && index !== null) {
        if (Array.isArray(selectedIndex.value) && append) {
            if (isSelected(index)) {
                result = selectedIndex.value.filter(v => v !== index)
            } else {
                result = [...selectedIndex.value, index]
            }
        } else {
            result = [index]
        }

        if (props.withEmpty && result.length > 1) {
            result = result.filter(v => v !== 0)
        }
    }

    selectedIndex.value = result
}

const select = (option: SelectOption | null, index: number | null = null, append = false) => {
    if (options.value.length !== optionsToRender.value.length && index !== null && index !== -1) {
        index = options.value.findIndex((option) => option.value === optionsToRender.value[index as number]!.value)
    }

    selectValue(option?.value, append)
    selectValueIndex(index, append)

    emit('update:option', option)

    if (!append) {
        query.value = ''

        if (index !== null) {
            moveFocusTo(index)
        } else {
            resetFocus()
        }
        close()
    }
}

const selectByIndex = (index: number, append = false) => {
    if (!optionsToRender.value[index]) {
        select(null)

        return
    }

    select(optionsToRender.value[index], index, append)
}

const selectAutocompleted = (append = false) => {
    if (!autocompletedOption.value) {
        return
    }

    select(autocompletedOption.value, null, append)
}

const isSelected = (index: number) => {
    // if (props.withEmpty && index === 0) {
    //     return false
    // }

    const option = options.value[index]

    if (!option) {
        return false
    }

    const optionValue = option.value

    // Multiple values
    if (props.multiple) {
        if (selectedIndex.value !== null) {
            // @ts-ignore
            return selectedIndex.value.includes(index)
        }

        return value.value?.includes(optionValue)
    }

    // Single value
    if (selectedIndex.value !== null) {
        return index === selectedIndex.value
    }

    return optionValue === selectedOption.value?.value
}

const isSelectedValue = (optionValue: any) => {
    if (props.multiple) {
        return value.value?.includes(optionValue)
    }

    return optionValue === selectedOption.value?.value
}

const onTab = (event: KeyboardEvent) => {
    if (autocompletedOption.value && autocompletedOption.value.value !== query.value) {
        event.preventDefault()
        selectAutocompleted(event.shiftKey)
    }
}

const fastSearch = ref('')
let timeout: NodeJS.Timeout | null = null

//

const {
    focusedIndex,
    isFocused,
    resetFocus,
    moveFocusTo,
    handleKeydown: moveFocus,
} = useKeyboardListNavigation(computed(() => {
    if (props.focusableOptionsLength !== undefined) {
        return Array.from({ length: props.focusableOptionsLength }).map(() => true)
    }

    return optionsToRender.value.map(() => true)
}), {
    loop: true,
    initialIndex: getIndexOfSelectedValue,
    onFocusMove(fromIndex, toIndex) {
        emit('focusMove', fromIndex, toIndex)
    },
})

watch(focusedIndex, (index) => {
    if (index !== -1) {
        const container = getOptionsContainer()
        const element = container?.querySelector('[data-index="' + index + '"]') as HTMLElement
        console.log(element)

        if (element) {
            scrollTo(element)
        }
    }
})

//

const onKeydown = (event: KeyboardEvent) => {
    if (!isActive.value && props.freeInput) {
        return
    }

    emit('keydown', event)

    if (event.code === 'Enter' || event.code === 'NumpadEnter') {
        event.preventDefault()
    }

    moveFocus(event)

    if (!isSearch.value) {
        fastSearch.value += event.key

        if (fastSearch.value) {
            const container = getOptionsContainer()

            container.querySelectorAll('.input-select__option__text').forEach((option: Element, index: number) => {
                if (option.textContent?.toLowerCase().startsWith(fastSearch.value.toLowerCase())) {
                    moveFocusTo(index)
                    scrollTo(option as HTMLElement)
                }
            })
        }

        if (timeout) {
            clearTimeout(timeout)
        }

        timeout = setTimeout(() => {
            fastSearch.value = ''
        }, 500)
    }
}

const onKeyup = (event: KeyboardEvent, handleSpace = true) => {
    if ((handleSpace && event.code === 'Space') || event.code === 'Enter' || event.code === 'NumpadEnter') {
        event.preventDefault()
        event.stopPropagation()
        onEnter(event)
    }

    if (event.code === 'ArrowRight') {
        selectAutocompleted(event.shiftKey)
    }

    if (event.code === 'Escape') {
        event.stopPropagation()
        event.stopImmediatePropagation()

        close()

        if (!props.freeInput) {
            clearInputs()
        }
    }
}

const onEnter = (event: KeyboardEvent) => {
    if (!isActive.value) {
        open()

        return
    }

    const append = event.shiftKey

    if (hasFocused.value) {
        selectFocused(append)
    } else if (autocompletedOption.value) {
        selectAutocompleted(append)
    } else {
        if (!append) {
            close()
        }
    }
}

// Search
// ================

const query = ref('')

const optionCanBeAutocompleted = (option: SelectOption) => {
    return option && option.title.substring(0, query.value.length).toLowerCase() === query.value.toLowerCase()
}

const autocompletedOption = computed(() => {
    if (!props.autocompleteSearch) {
        return null
    }

    if (!isActive.value && props.freeInput) {
        return null
    }

    if (focusedIndex.value !== -1) {
        const option = options.value[focusedIndex.value]

        if (option && optionCanBeAutocompleted(option)) {
            return option
        }
    }

    if (query.value.length) {
        if (optionsToRender.value.length) {
            if (props.withEmpty && optionsToRender.value.length === 1) {
                return null
            }

            const option = optionsToRender.value.at((props.withEmpty ? 1 : 0))

            if (option && optionCanBeAutocompleted(option)) {
                return option
            }
        }
    }

    return null
})

const autocompleteTitle = computed(() => {
    if (autocompletedOption.value) {
        const foundPart = autocompletedOption.value.title.substring(0, query.value.length)

        if (foundPart !== query.value) {
            return query.value + autocompletedOption.value.title.substring(query.value.length)
        }

        return autocompletedOption.value.title
    }

    if (query.value.length) {
        return ''
    }

    return ''

    // return props.multiple ? '' + selectedOptionsResult.value : selectedOption.value?.title
})

const isSearch = computed(() => props.search || props.filter)

const handleSearch = (event: Event) => {
    open()
    emit('search', (event.target as HTMLInputElement).value)
}

const handleSearchDebounced = props.debounce ? useDebounceFn(
    handleSearch,
    typeof props.debounce === 'boolean' ? 300 : props.debounce,
) : handleSearch

const clearInputs = () => {
    if (query.value.length) {
        query.value = ''
    }

    resetFocus()

    emit('clear')
}

const onClearClick = () => {
    close()

    if (!props.freeInput) {
        clearInputs()
    }
}

watch(isActive, () => {
    nextTick(() => {
        if (isActive.value) {
            input.value?.focus()
        }
    })
})

// Focus manipulation
// ================
watch(query, () => {
    resetFocus()
})

const focus = () => {
    input.value?.focus()
}

const selectFocused = (append = false) => {
    selectByIndex(focusedIndex.value, append)
}

const hasFocused = computed(() => focusedIndex.value !== -1)

const onInputFocus = (event: FocusEvent) => {
    open()

    emit('focus', event)
}

const onInputBlur = (event: FocusEvent) => {
    emit('blur', event)

    // const container = props.optionsContainer ?? dropdown.value?.$el
    //
    // // @ts-ignore
    // if (!_hasParent(event.relatedTarget || event.target, container)) {
    //     if (!props.freeInput) {
    //         clearInputs()
    //     }
    //
    //     resetFocus()
    //
    //     close()
    // }
}

const handleClickOutside = () => {
    if (!props.freeInput) {
        clearInputs()
    }

    resetFocus()
}

const _hasParent = (element: HTMLElement, parent: HTMLElement | null) => {
    let el = element?.parentElement

    if (!el) {
        return false
    }

    if (element === parent) {
        return true
    }

    while (el) {
        if (!el) {
            break
        }

        if (el === parent) {
            return true
        }

        el = el.parentElement
    }

    return false
}

const updateQuery = (event: Event) => {
    query.value = (event.target as HTMLInputElement).value

    handleSearchDebounced(event)

    if (props.freeInput) {
        selectValue(query?.value)
    }
}

// ================

onMounted(() => {
    //     if (value.value === null && !props.withEmpty) {
    //         selectByIndex(0)
    //     }

    if (props.autofocus) {
        focus()
    }
})

//

const sameWidthPopperModifier: Modifier<any, any> = {
    name: 'sameWidth',
    enabled: true,
    phase: 'beforeWrite',
    requires: ['computeStyles'],
    fn: ({ state }) => {
        if (!state.styles.popper) {
            return
        }

        state.styles.popper.width = `${state.rects.reference.width}px`
    },
    effect: ({ state }) => {
        state.elements.popper.style.width = `${
            (state.elements.reference as HTMLElement).offsetWidth
        }px`
    },
}

// ================

defineExpose({
    open,
    close,
    toggle,
    focus,
    select,
    focused: focusedIndex,
    isFocused,
    isSelected,
    query,
})
</script>
