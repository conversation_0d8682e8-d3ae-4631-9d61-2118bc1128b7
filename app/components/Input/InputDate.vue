<template>
    <div class="input-date">
        <InputText
            ref="inputRef"
            v-model="maskedInputValue"
            :name="name"
            type="text"
            :required="required"
            class="input-date__field"
            :data-has-value="Boolean(internalValue?.length)"
            autocomplete="off"
        />

        <span
            ref="ghostTextRef"
            class="input-date__ghost"
            aria-hidden="true"
        >
            {{ internalValue }}
        </span>

        <span
            v-if="internalValue.length"
            class="input-date__placeholder"
            :style="{ left: placeholderOffset + 'px' }"
        >
            {{ remainingPlaceholder }}
        </span>

        <button class="input-date__datepicker-button" @click="openDatepicker">
            <IconCalendar />
        </button>
    </div>
</template>

<script setup lang="ts">
import {
    computed,
    onBeforeUnmount,
    onMounted,
    ref,
    watch,
} from 'vue'
import { MaskInput } from 'maska'
import { useNewModal } from '@tmg/modals'
import DatepickerModal from '~/components/DatepickerModal.vue'
import dayjs from 'dayjs'

// Props
const props = withDefaults(defineProps<{
    name: string
    modelValue?: string | null | undefined // YYYY-MM-DD
    required?: boolean
    maxDate?: Date | null | undefined
    minDate?: Date | null | undefined
}>(), {
    modelValue: undefined,
    required: false,
    maxDate: undefined,
    minDate: undefined,
})

const emit = defineEmits<{
    'update:modelValue': [value: string | undefined]
}>()

// Refs
const inputRef = ref()
const ghostTextRef = ref<HTMLSpanElement>()
const placeholderOffset = ref(0)

const placeholderMask = '__/__/____'

const internalValue = ref('')

watch(() => props.modelValue, (value) => {
    if (value === undefined || value === null || value === '') {
        internalValue.value = ''

        return
    }

    const parsedDate = parseDatestring(value ?? '')

    if (!isValidDate(parsedDate)) {
        return
    }

    internalValue.value = formatToInputDate(parsedDate)
}, {
    immediate: true,
})

/**
 * Display using MM/DD/YYYY format
 * Store using YYYY-MM-DD format
 */
const maskedInputValue = computed({
    get() {
        return internalValue.value
    },
    set(value: string) {
        internalValue.value = value

        const date = parseInputDatestring(value)

        // if (!isValidDate(date)) {
        //     return
        // }

        emit('update:modelValue', formatToValueDate(date))
    },
})

type ParsedDate = {
    'MM': string | undefined
    'YYYY': string | undefined
    'DD': string | undefined
} | undefined

function parseDatestring(value: string): ParsedDate {
    const [YYYY, MM, DD] = value.split('-')

    return {
        MM,
        DD,
        YYYY,
    }
}

function parseInputDatestring(value: string): ParsedDate {
    const [MM, DD, YYYY] = value.split('/')

    return {
        MM,
        DD,
        YYYY,
    }
}

function formatToInputDate(date: ParsedDate): string {
    if (!date) {
        return ''
    }

    const {
        MM,
        DD,
        YYYY,
    } = date

    return `${MM}/${DD}/${YYYY}`
}

const formatToValueDate = (date: ParsedDate): string => {
    if (!date) {
        return ''
    }

    const {
        MM,
        DD,
        YYYY,
    } = date

    return `${YYYY}-${MM?.padStart(2, '0')}-${DD?.padStart(2, '0')}`
}

function parsedDateToDateObject(date: ParsedDate): Date | undefined {
    if (!date || !isValidDate(date)) {
        return undefined
    }

    const {
        MM,
        DD,
        YYYY,
    } = date

    return new Date(`${YYYY}-${MM}-${DD}T12:00:00Z`)
}

function isValidDate(date: ParsedDate): boolean {
    if (!date) {
        return false
    }

    const {
        MM,
        DD,
        YYYY,
    } = date

    if (!MM || !DD || !YYYY) {
        return false
    }

    if (MM.length !== 2 || DD.length !== 2 || YYYY.length !== 4) {
        return false
    }

    const parsedDate = new Date(`${YYYY}-${MM}-${DD}T12:00:00Z`)

    return !isNaN(parsedDate.getTime())
}

/**
 * Remaining placeholder after current input
 */
const remainingPlaceholder = computed(() => {
    return placeholderMask.slice(internalValue.value.length)
})

/**
 * Setup mask & ghost alignment
 */
let maskInstance: MaskInput

function recalculatePlaceholder() {
    const ghostWidth = Math.ceil(ghostTextRef.value?.getBoundingClientRect().width ?? 0)
    placeholderOffset.value = ghostWidth + 16 + 2 // 16px padding + 2px spacing
}

onMounted(() => {
    // Setup mask
    maskInstance = new MaskInput(inputRef.value.$el, {
        mask: '##/##/####',
    })

    // Watch width of ghost value to align placeholder
    watch(internalValue, () => {
        recalculatePlaceholder()
    }, {
        immediate: true,
        flush: 'post',
    })
})

onBeforeUnmount(() => {
    maskInstance?.destroy()
})

//

const modal = useNewModal(DatepickerModal)

function openDatepicker() {
    const value = internalValue.value
        ? parsedDateToDateObject(parseDatestring(internalValue.value))
        : undefined

    modal.open({
        modelValue: value || new Date(),
        onApply(value: Date) {
            maskedInputValue.value = dayjs(value).format('MM/DD/YYYY')
        },
        datepickerOptions: {
            canNavigateForward: (calendarDates: Date[]) => {
                if (!props.maxDate) {
                    return true
                }

                return calendarDates.some(date => dayjs(date).add(1, 'month').isBefore(props.maxDate))
            },
            canNavigateBackward: (calendarDates: Date[]) => {
                if (!props.minDate) {
                    return true
                }

                return calendarDates.some(date => dayjs(date).add(-1, 'month').isAfter(props.minDate))
            },
            calendarProps: {
                disabledDates(date: dayjs.Dayjs): boolean {
                    if (props.maxDate && props.minDate) {
                        return date.isAfter(props.maxDate) || date.isBefore(props.minDate)
                    } else if (props.minDate) {
                        return date.isBefore(props.minDate)
                    } else if (props.maxDate) {
                        return date.isAfter(props.maxDate)
                    }

                    return false
                },
            },
        },
        valueMode: 'day',
    })
}
</script>
