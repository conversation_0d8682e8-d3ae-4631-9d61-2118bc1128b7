<template>
    <input
        type="radio"
        :name="name"
        class="input-radio"
        :value="value"
        :required="required"
        :disabled="disabled"
        :checked="checked"
        @change="check"
    >
</template>

<script setup lang="ts" generic="T">
defineOptions({
    name: 'InputRadio',
})

const props = withDefaults(defineProps<{
    name: string,
    value: T,
    modelValue?: T,
    disabled?: boolean,
    required?: boolean,
    checked?: boolean,
}>(), {
    modelValue: undefined,
    disabled: false,
    required: false,
    size: undefined,
    checked: undefined,
})

const emit = defineEmits<{
    'update:modelValue': [value: T],
    'change': [value: T],
}>()

const checked = computed(() => {
    if (props.checked === undefined) {
        return props.modelValue === props.value
    }

    return props.checked
})

function check() {
    emit('update:modelValue', props.value)
    emit('change', props.value)
}
</script>
