<template>
    <div class="input-phone">
        <InputSelect
            v-model="country"
            :options="options"
            data-has-value="true"
            no-arrow
            class="flex items-center"
            filter
            :search-placement="'inContent'"
        >
            <template #result-text>
                <div class="h-full flex gap-2 w-full items-center justify-between px-4 rounded-xl">
                    <div class="vti__flag flex-none" :class="country?.toLowerCase()" />
                    <span>+{{ prefix }}</span>
                    <div class="input-select__arrow">
                        <IconChevron class="flex-none w-2.5 h-2.5 text-[#4F5E71]" />
                    </div>
                </div>
            </template>

            <template #option="{ index, option, selected, select, optionsToRender, hasGroup }">
                <div
                    v-if="hasGroup(option) && (!optionsToRender[index-1] || optionsToRender[index-1]?.group !== option.group)"
                    class="input-select__option--group my-2"
                >
                    <div class="input-select__option__text">
                        <div class="input-select__option__text__title">
                            {{ option.group }}
                        </div>
                    </div>
                </div>
                <div
                    class="input-select__option"
                    :class="{ 'bg-slate-200': selected, 'input-select__option--selected': selected }"
                    @click="select"
                >
                    <div class="min-w-8">
                        <div class="vti__flag" :class="option.value.toLowerCase()" />
                    </div>
                    <span class="grow truncate">
                        {{ option.title }}
                    </span>
                    <span class="min-w-12">
                        +{{ (option as typeof options[0]).phonePrefix }}
                    </span>
                </div>
            </template>

            <template #mobile-option="{ option }">
                {{ option.title }} (+{{ (option as typeof options[0]).phonePrefix }})
            </template>
        </InputSelect>

        <input
            v-model="value"
            :placeholder="placeholder"
            :name="name"
            type="tel"
            class="grow rounded-xl min-w-0 focus:outline-none"
            @input="filterNumbers"
        >
    </div>
</template>

<script setup lang="ts">
import examples from 'libphonenumber-js/mobile/examples'
import metadata from 'libphonenumber-js/metadata.full.json'
import countries from 'assets/countries'
import type { PhoneNumber } from 'libphonenumber-js'
import { type CountryCode } from 'libphonenumber-js'
import parsePhoneNumber, { AsYouType, getExampleNumber } from 'libphonenumber-js'

import '~/assets/css/vendor/flags.css'
import { useCountryOptions } from '~/composables/useCountryOptions'
import { Mask } from 'maska'

const props = defineProps<{
    modelValue: string | undefined,
    name: string,
}>()

const emit = defineEmits<{
    'update:modelValue': [value: string],
}>()

const country = ref<CountryCode>('US')

const { options } = useCountryOptions()

const valueBeforeEmit = ref()

const value = computed({
    get() {
        const parsed = parsePhoneNumber(props.modelValue ?? '')

        if (!parsed) {
            return valueBeforeEmit.value ?? ''
        }

        if (country.value === 'US') {
            const mask = '(###) ###-####'

            return new Mask({ mask }).masked(parsed.formatInternational().slice(parsed.countryCallingCode.length + 1).replace(/\D/g, ''))
        }

        return new AsYouType(country.value).input(parsed.formatInternational()).slice(parsed.countryCallingCode.length + 1).trim()
    },
    set(value) {
        update(value)
    },
})

const example = computed(() => {
    if (!country.value) {
        return
    }

    return getExampleNumber(country.value, examples)
})

const parsed = ref<ReturnType<typeof parsePhoneNumber>>()

const placeholder = computed(() => {
    const phone = example.value

    if (!phone) {
        return
    }

    return phone.formatInternational().slice(phone.countryCallingCode.length + 1).trim()
})

const prefix = computed(() => example.value?.countryCallingCode)

const update = (value: string) => {
    const phone = parsePhoneNumber(value, value.startsWith('+') ? undefined : country.value)
    valueBeforeEmit.value = value

    if (!phone) {
        emitModelValue('')

        return
    }

    parsed.value = phone

    const international = phone.formatInternational()

    if (international.startsWith('+' + phone.countryCallingCode)) {
        value = international.slice(phone.countryCallingCode.length + 1).trim()
    }

    const prefix = phone.countryCallingCode

    const countryCode = getCountry(parsed.value)

    if (countryCode && country.value !== countryCode) {
        country.value = countryCode as CountryCode
    }

    const normalized = '+' + (prefix + value).replace(/\D/g, '')

    valueBeforeEmit.value = value
    emitModelValue(normalized)
}

function getCountry(parsedValue?: PhoneNumber): CountryCode {
    if (!parsedValue) {
        return country.value
    }

    let result = parsedValue.country

    if (!parsedValue.country) {
        let possibleCountries = parsedValue.getPossibleCountries()

        if (!possibleCountries.length && parsedValue.countryCallingCode) {
            possibleCountries = metadata.country_calling_codes[parsedValue.countryCallingCode] ?? []
        }

        if (possibleCountries.length) {
            const foundCountry = possibleCountries[0]!

            const foundCountryPrefix = countries[foundCountry]?.dialCode
            const selectedCountryPrefix = countries[country.value]?.dialCode

            if (foundCountryPrefix !== selectedCountryPrefix) {
                result = foundCountry
            }
        }
    }

    if (!result) {
        result = country.value
    }

    return result
}

const emitModelValue = (value: string) => {
    emit('update:modelValue', value)
}

function filterNumbers(event: Event) {
    const input = event.target as HTMLInputElement

    input.value = input.value.replace(/[^\d-+ ()]/g, '').replace(/\s{2,}/g, ' ')
}

if (props.modelValue) {
    update(props.modelValue)
}

watch(country, () => {
    update(value.value)
})

// watch(() => props.modelValue, (value) => {
//     update((valueBeforeEmit.value || value) ?? '')
// })
</script>
