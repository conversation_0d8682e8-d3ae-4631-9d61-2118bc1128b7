<template>
    <input
        class="input-text"
        :type="type"
        :name="name"
        :value="value"
        :pattern="pattern"
        :maxlength="maxlength"
        :min="min"
        :max="max"
        :inputmode="inputmode"
        :autocomplete="autocomplete"
        :data-has-value="!isEmpty(value)"
        @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
        @keydown="$emit('keydown', $event)"
        @keypress="$emit('keypress', $event)"
        @paste="$emit('paste', $event)"
        @beforeinput="$emit('beforeinput', $event as InputEvent)"
        @blur="$emit('blur', $event)"
    >
</template>

<script setup lang="ts">
import { useCurrentElement } from '@vueuse/core'
import { isEmpty } from '~/helpers/UtilsHelper'

defineOptions({
    name: 'InputText',
})

const props = withDefaults(defineProps<{
    name: string,
    modelValue?: string,
    type?: string,
    pattern?: string,
    maxlength?: string | number,
    min?: number,
    max?: number,
    inputmode?: 'text' | 'search' | 'email' | 'tel' | 'url' | 'none' | 'numeric' | 'decimal' | undefined,
    autocomplete?: string,
    autofocus?: boolean,
}>(), {
    modelValue: undefined,
    type: 'text',
    pattern: undefined,
    maxlength: undefined,
    min: undefined,
    max: undefined,
    inputmode: undefined,
    autocomplete: undefined,
    autofocus: false,
})

const emit = defineEmits<{
    'update:modelValue': [value: string],
    keydown: [event: KeyboardEvent],
    keypress: [event: KeyboardEvent],
    paste: [event: ClipboardEvent],
    beforeinput: [event: InputEvent],
    blur: [event: FocusEvent],
}>()

const value = computed({
    get: () => props.modelValue ?? '',
    set: (value: string) => emit('update:modelValue', value),
})

const el = useCurrentElement<HTMLElement>()

if (props.autofocus) {
    onMounted(() => {
        el.value?.focus()
    })
}

defineExpose({
    input: el,
})
</script>
