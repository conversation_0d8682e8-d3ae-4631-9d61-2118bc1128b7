<template>
    <div
        class="input-number"
        :class="{
            '--hide-controls': customControls,
        }"
    >
        <button
            v-if="customControls"
            class="input-number__iterator"
            :disabled="isMin"
            @click="increment(-1)"
        >
            <IconMinus />
        </button>

        <InputText
            ref="inputRef"
            v-model="inputValue"
            type="number"
            :name="name"
            :min="min"
            :max="max"
            :maxlength="maxlength"
            :autocomplete="autocomplete"
            :autofocus="autofocus"
            inputmode="numeric"
            @keydown="$emit('keydown', $event)"
            @keypress="$emit('keypress', $event)"
            @paste="$emit('paste', $event)"
            @beforeinput="$emit('beforeinput', $event)"
            @blur="$emit('blur', $event)"
        />

        <button
            v-if="customControls"
            class="input-number__iterator"
            :disabled="isMax"
            @click="increment(1)"
        >
            <IconPlus />
        </button>
    </div>
</template>

<script setup lang="ts">
defineOptions({
    name: 'InputNumber',
})

const props = withDefaults(defineProps<{
    name: string,
    modelValue?: number,
    type?: string,
    pattern?: string,
    maxlength?: string | number,
    min?: number | undefined,
    max?: number | undefined,
    autocomplete?: string,
    autofocus?: boolean,
    customControls?: boolean,
    incrementStep?: number |((direction: 1 | - 1, value: number) => number),
}>(), {
    modelValue: undefined,
    type: 'text',
    pattern: undefined,
    maxlength: undefined,
    min: 0,
    max: undefined,
    autocomplete: undefined,
    autofocus: false,
    customControls: false,
    incrementStep: undefined,
})

const emit = defineEmits<{
    'update:modelValue': [value: number | undefined],
    keydown: [event: KeyboardEvent],
    keypress: [event: KeyboardEvent],
    paste: [event: ClipboardEvent],
    beforeinput: [event: InputEvent],
    blur: [event: FocusEvent],
}>()

const inputValue = computed({
    get() {
        return String(props.modelValue ?? '')
    },
    set(val: string | number) {
        let numberValue = typeof val === 'string' ? parseInt(val) : val

        if (val === '' || val === null || val === undefined) {
            emit('update:modelValue', undefined)
        }

        if (isNaN(numberValue)) {
            return
        }

        if (props.min !== undefined && numberValue < props.min) {
            numberValue = props.min
        }

        if (props.max !== undefined && numberValue > props.max) {
            numberValue = props.max
        }

        emit('update:modelValue', numberValue)

        const input = inputRef.value!.input

        if (String(inputRef.value?.input?.value) !== String(numberValue)) {
            input!.value = String(numberValue)
        }
    },
})

const inputRef = ref<{ input: HTMLInputElement }>()

const currentValue = computed(() => props.modelValue || 0)

const isMin = computed(() => props.min !== undefined && currentValue.value <= props.min)
const isMax = computed(() => props.max !== undefined && currentValue.value >= props.max)

function increment(direction: 1 | -1) {
    const incrementResolver = typeof props.incrementStep === 'function' ? props.incrementStep : () => 1

    inputValue.value = currentValue.value + direction * incrementResolver(direction, currentValue.value)
}
</script>
