<template>
    <button
        v-if="!isDisabled"
        id="jivo_callback_btn"
        class="jivo-callback-btn"
        @click="handleCallbackClick"
    >
        <slot>
            Request Callback
        </slot>
    </button>
</template>

<script setup lang="ts">
interface Props {
    /**
     * Additional CSS classes
     */
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    class: '',
})

const { openCallbackButton, isDisabled } = useJivo()

const handleCallbackClick = () => {
    openCallbackButton()
}

// Global function for initialization
onMounted(() => {
    if (import.meta.client) {
        // Set global function for Jivo callback
        window.jivo_onLoadCallback = () => {
            const btn = document.getElementById('jivo_callback_btn')

            if (btn) {
                btn.addEventListener('click', handleCallbackClick)
            }
        }
    }
})
</script>

<style scoped>
.jivo-callback-btn {
    /* Base button styles */
    background: linear-gradient(135deg, #9d1d5a 0%, #590c32 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-family: inherit;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.jivo-callback-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(157, 29, 90, 0.3);
}

.jivo-callback-btn:active {
    transform: translateY(0);
}
</style>
