<template>
    <div class="menu-modal">
        <div class="menu-modal__container">
            <div class="menu-modal__close">
                <button
                    class="menu-modal__close__button"
                    type="button"
                    @click="close"
                >
                    <IconArrowRight class="w-4 h-4" />
                </button>
            </div>

            <div class="menu-modal__content">
                <div class="menu-modal__content__padding">
                    <nav class="menu-modal__menu">
                        <NuxtLink
                            v-for="(item, index) in menuItems"
                            :key="index"
                            class="menu-modal__menu__item"
                            target="_blank"
                            :class="{
                                'menu-modal__menu__item--active': activePage === item.route
                            }"
                            :to="{ path: item.route }"
                        >
                            {{ item.title }}
                        </NuxtLink>
                    </nav>
                    <div class="menu-modal__agent">
                        <div class="menu-modal__agent__avatar">
                            <AppPicture
                                src="/images/index-sections/user-2.jpg"
                                alt="Agent"
                                loading="lazy"
                                :img-attrs="{ class: 'rounded-full' }"
                            />
                            <span class="menu-modal__agent__avatar__online" />
                        </div>
                        <b class="menu-modal__agent__name">Daniel</b>
                        <span class="menu-modal__agent__contact ">{{ $site.phone }}</span>
                        <a :href="$site.call" class="menu-modal__agent__call button">Call</a>
                    </div>
                    <!--                <div v-if="agent" class="menu-modal__agent">-->
                    <!--                    <div-->
                    <!--                        class="menu-modal__agent__avatar"-->
                    <!--                        :class="{-->
                    <!--                            'menu-modal__agent__avatar&#45;&#45;online': agent.online,-->
                    <!--                        }"-->
                    <!--                    >-->
                    <!--                        <img-->
                    <!--                            :src="agent.avatar"-->
                    <!--                            alt="Agent image"-->
                    <!--                            class="menu-modal__agent__avatar__image"-->
                    <!--                            height="60"-->
                    <!--                            width="60"-->
                    <!--                        >-->
                    <!--                    </div>-->
                    <!--                    <div class="menu-modal__agent__info">-->
                    <!--                        <div class="menu-modal__agent__name">-->
                    <!--                            {{ agent.name }}-->
                    <!--                        </div>-->
                    <!--                        <a :href="getCallLink(agent.phone)" class="menu-modal__agent__phone">-->
                    <!--                            <IconPhone />-->
                    <!--                            {{ formatPhone(agent.phone) }}-->
                    <!--                        </a>-->
                    <!--                    </div>-->
                    <!--                </div>-->
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useCurrentModal, useModal } from '@tmg/modals'
import { useMenuItems } from '~/composables/useMenuItems'
import { formatPhone, getCallLink } from '~/helpers/PhoneHelper'

defineOptions({
    name: 'MenuModal',
    modal: {
        attrs: {
            class: 'modal--right modal-transition--slide-left',
            style: '--modal-overlay-background: rgba(51, 51, 51, 0.8)',
        },
    },
})

// defineProps<{
//     agent?: Agent,
// }>()

//

const { close } = useModal(useCurrentModal())

//

const { menuItems } = useMenuItems()

//

const router = useRouter()

const activePage = computed(() => {
    return router.currentRoute.value.path
})
</script>
