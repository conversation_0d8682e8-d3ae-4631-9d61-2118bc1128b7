<template>
    <Datepicker v-bind="datepickerOptions" v-model="value">
        <template #footer>
            <div class="datepicker__footer">
                <button
                    type="button"
                    class="datepicker__footer__button"
                    @click="cancel"
                >
                    Cancel
                </button>
                <button
                    type="button"
                    class="datepicker__footer__button"
                    @click="apply"
                >
                    Ok
                </button>
            </div>
        </template>
    </Datepicker>
</template>

<script setup lang="ts">
import type { DatepickerOptions } from '~/composables/useDatepicker'
import type { CalendarValue } from '~/composables/useCalendar'

defineOptions({
    name: 'DatepickerModal',

    modal: {
        attrs: {
            class: 'modal--center modal-transition--scale-up',
        },
    },
})

const props = defineProps<{
    modelValue: CalendarValue,
    onApply(value: CalendarValue): void,
    datepickerOptions?: DatepickerOptions,
}>()

const emit = defineEmits<{
    close: [],
}>()

const value = ref<CalendarValue>(props.modelValue ?? new Date())

//

function cancel() {
    emit('close')
}

function apply() {
    props.onApply(value.value)
    emit('close')
}
</script>
