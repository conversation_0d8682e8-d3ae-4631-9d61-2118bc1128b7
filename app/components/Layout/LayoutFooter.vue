<template>
    <footer class="footer">
        <div class="footer__section">
            <div class="footer__section__container">
                <div class="footer__contacts">
                    <h2 class="footer__contacts__title">
                        Contact us
                    </h2>
                    <div class="footer__contacts__item mt-0">
                        <div class="footer__contacts__item__icon-container">
                            <div class="footer__contacts__item__icon-container__icon brand-light-color">
                                <IconPhoneContacts />
                            </div>
                        </div>
                        <div class="footer__contacts__item__info-container">
                            <span class="footer__contacts__item__info-container__title">
                                Phone
                            </span>
                            <a :href="$site.call" class="footer__contacts__item__info-container__value">
                                {{ $site.phone }}
                            </a>
                            <a :href="$site.call" class="lg:hidden button button--primary mt-7">Call now</a>
                        </div>
                    </div>
                    <div class="footer__contacts__item mt-12 lg:mt-8">
                        <div class="footer__contacts__item__icon-container">
                            <div class="footer__contacts__item__icon-container__icon brand-light-color">
                                <IconMail />
                            </div>
                        </div>
                        <div class="footer__contacts__item__info-container">
                            <span class="footer__contacts__item__info-container__title">
                                Email
                            </span>
                            <a
                                :href="`mailto:${$site.email}`"
                                class="footer__contacts__item__info-container__value footer__contacts__item__info-container__value--brand"
                            >
                                {{ $site.email }}
                            </a>
                            <a :href="`mailto:${$site.email}`" class="lg:hidden button button--primary mt-7">Email
                                Us</a>
                        </div>
                    </div>
                    <div class="footer__contacts__item mt-12 lg:mt-8">
                        <div class="footer__contacts__item__icon-container">
                            <div class="footer__contacts__item__icon-container__icon brand-light-color">
                                <IconMarker />
                            </div>
                        </div>
                        <div class="footer__contacts__item__info-container">
                            <span class="footer__contacts__item__info-container__title">
                                Address
                            </span>
                            <span
                                class="footer__contacts__item__info-container__value footer__contacts__item__info-container__value--brand"
                            >
                                {{ $site.address }}
                            </span>
                            <a :href="`mailto:vip@${$site.domain}`" class="lg:hidden button button--primary mt-7">Search
                                on map</a>
                        </div>
                    </div>
                    <div class="footer__contacts__link-items">
                        <a
                            class="footer__contacts__item__link"
                            href="https://www.facebook.com/Travelbusinessclasscom-101902872045615"
                            target="_blank"
                        >
                            <IconFacebook />
                        </a>
                        <a
                            class="footer__contacts__item__link"
                            href="https://www.linkedin.com/company/travel-business-class/"
                            target="_blank"
                        >
                            <IconLinkedin />
                        </a>
                    </div>
                </div>
                <div class="footer__info">
                    <h2 class="footer__info__title ">
                        Contact us
                    </h2>
                    <p class="footer__info__terms">
                        All the fares displayed are in USD and include all taxes, fees and applicable surcharges. All
                        prices are 'from' per person, based on business class weekday travel (Monday - Thursday) from
                        the USA, and depend on the chosen class of service, departure city, airline and the route.
                        Lowest transatlantic fares are usually from the East Coast; transpacific fares - from the West
                        Coast, fares to Latin America - from Florida. TravelBusinessClass is not able to identify some
                        travel partners or itinerary details online so as not to directly compete with retail sales of
                        our partners. Savings up to 60% off are indicated of the full unrestricted published prices of
                        major airlines and may vary based on individual fare rules. Some airlines may impose additional
                        baggage charges. The fares are subject to seat availability in the corresponding booking
                        inventory. Seats are limited and may not be available on all flights and dates. The fares have
                        flexible booking policy: free exchange and/or cancelation is available now with our partner
                        airlines. In the event of trip cancelation, unless otherwise stated, customers will retain
                        travel credit with the issuing airline for a period determined by the airline policy , excluding
                        airline fees. In the event of exchange, fare difference may apply. The fares and their governing
                        rules are subject to change without prior notice. Other restrictions may apply.
                    </p>
                    <div class="footer__info__company">
                        <p class="mr-2 mt-2 md:mt-0">
                            © {{ currentYear }} — <b>TravelBusinessClass.com</b>
                        </p>
                        <div class="mt-2 md:mt-0">
                            <a
                                href="https://travelbusinessclass.com/page/privacy_policy"
                                class="uppercase text-gradient text-[11px]"
                            >
                                Privacy policy
                            </a>
                            <span class="text-gradient text-xs mx-1.5">
                                •
                            </span>
                            <a
                                href="https://travelbusinessclass.com/page/terms_and_conditions"
                                class="uppercase text-gradient text-[11px]"
                            >
                                Terms and conditions
                            </a>
                        </div>
                    </div>
                    <div class="footer__info__partners">
                        <AppPicture
                            src="/images/footer/iata.png"
                            alt="International Air Transport Association"
                            loading="lazy"
                            :img-attrs="{ title: 'International Air Transport Association', class: 'h-6' }"
                        />
                        <AppPicture
                            src="/images/footer/visa.png"
                            alt="Visa"
                            loading="lazy"
                            :img-attrs="{ title: 'Visa', class: 'h-4' }"
                        />
                        <AppPicture
                            src="/images/footer/master.png"
                            alt="Mastercard"
                            loading="lazy"
                            :img-attrs="{ title: 'Mastercard', class: 'h-6' }"
                        />
                        <AppPicture
                            src="/images/footer/arc-logo.png"
                            alt="Arc"
                            loading="lazy"
                            :img-attrs="{ title: 'Arc', class: 'h-6' }"
                        />
                        <AppPicture
                            src="/images/footer/american-express.png"
                            alt="American Express"
                            loading="lazy"
                            :img-attrs="{ title: 'American Express', class: 'h-6' }"
                        />
                        <AppPicture
                            src="/images/footer/discover.png"
                            alt="Discover"
                            loading="lazy"
                            :img-attrs="{ title: 'Discover', class: 'h-4' }"
                        />
                        <AppPicture
                            src="/images/footer/authorize.png"
                            alt="Authorize.Net"
                            loading="lazy"
                            :img-attrs="{ title: 'Authorize.Net', class: 'h-4' }"
                        />
                        <AppPicture
                            src="/images/footer/florida-department.png"
                            alt="Florida Department of Agriculture and Consumer Services"
                            loading="lazy"
                            :img-attrs="{ title: 'Florida Department of Agriculture and Consumer Services', class: 'h-8' }"
                        />
                        <AppPicture
                            src="/images/footer/eureka.png"
                            alt="Great Seal of California"
                            loading="lazy"
                            :img-attrs="{ title: 'Great Seal of California', class: 'h-8' }"
                        />
                    </div>
                </div>
            </div>
        </div>
    </footer>
</template>

<script setup lang="ts">
defineOptions({
    name: 'LayoutFooter',
})

const currentYear = new Date().getFullYear()
</script>
