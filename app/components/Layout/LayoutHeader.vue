<template>
    <header class="header">
        <div class="header__main">
            <AppLogo class="header__logo" />

            <nav class="header__menu">
                <template v-for="(item, index) in menuItems" :key="index">
                    <NuxtLink
                        v-if="!item.isDropdown"
                        class="header__menu__item"
                        target="_blank"
                        :class="{ '--active': activePage === item.route }"
                        :to="{ path: item.route }"
                    >
                        {{ item.title }}
                    </NuxtLink>

                    <AppMenuDropdown v-else :item="item" />
                </template>
            </nav>

            <div class="header__description">
                <p class="header__description__title">
                    {{ $site.logoText }}
                </p>
                <a :href="$site.call" class="font-bold block text-white leading-none text-sm">
                    {{ $site.phone }}
                </a>
            </div>

            <div class="header__right">
                <div class="header__right__call">
                    <div class="header__right__info">
                        <span class="header__right__info-text">Call us 24/7</span>
                        <a :href="$site.call" class="header__right__phone-link">
                            <IconPhone class="header__right__phone-icon" />
                            {{ $site.phone }}
                        </a>
                    </div>
                    <button
                        type="button"
                        aria-label="Call us"
                        class="header__right__call-button"
                    >
                        <IconPhoneBig class="w-5 h-5" />
                    </button>
                </div>
                <button
                    class="header__menu-button"
                    type="button"
                    @click="openMenu()"
                >
                    <IconMenu />
                </button>
            </div>
        </div>

        <div v-if="'default' in $slots" class="header__secondary">
            <slot />
        </div>
    </header>
</template>

<script setup lang="ts">
import { useMenuItems } from '~/composables/useMenuItems'

defineOptions({
    name: 'LayoutHeader',
})

//

const menu = useMenuStore()

function openMenu() {
    menu.open()
}

const { menuItems } = useMenuItems()

const router = useRouter()

const activePage = computed(() => {
    return router.currentRoute.value.path
})
</script>
