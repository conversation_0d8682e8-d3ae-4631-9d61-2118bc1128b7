/**
 * Google Analytics and Enhanced Ecommerce types for TravelBusinessClass
 */

// ========== Basic Types ==========

export type ItineraryClass = 'premium' | 'business' | 'first'
export type ItineraryType = 'oneWay' | 'roundTrip' | 'multi'

export interface ItineraryClassMap {
    premium: 'Premium Class'
    business: 'Business Class'
    first: 'First Class'
}

export interface ItineraryTypeMap {
    oneWay: 'One Way'
    roundTrip: 'Round Trip'
    multi: 'Multi City'
}

// ========== UTM Tracking Types ==========

export interface UtmParameters {
    utm_source?: string
    utm_medium?: string
    utm_campaign?: string
    utm_content?: string
    utm_term?: string
    gclid?: string
    gbraid?: string
    gad_source?: string
}

export interface UtmVisit extends UtmParameters {
    id?: number
    ga?: string // Google Analytics Client ID
    did?: string // Device ID
    referer?: string
    url?: string
    ip?: string
    ua?: string // User Agent
    created_at?: number
}

// ========== Product Types ==========

export interface AnalyticsProduct {
    name: string // Route like "JFK - LHR"
    id: string // Unique ID like "JFK2LHR"
    price: number
    category: string // Trip type
    list: string // Service class
    position?: number
    quantity?: number
}

export interface ProductPrice {
    type: ItineraryClass
    price: number
    oldPrice: number
}

export interface BookingFormData {
    price: ProductPrice[]
    passengers: {
        ADT: number // Adults
        CHD: number // Children
        INF: number // Infants
    }
    contact: {
        name: string
        phone: string
        email: string
        sendBySms: boolean
    }
    itinerary: Array<{
        from: any
        to: any
        departureDate: Date | null
        returnDate: Date | null
    }>
}

// ========== Enhanced Ecommerce Event Types ==========

export interface EcommerceActionField {
    list?: string
    id?: string
    affiliation?: string
    revenue?: number
    tax?: number
    shipping?: number
    coupon?: string
}

export interface EcommerceDetail {
    actionField: EcommerceActionField
    products: AnalyticsProduct[]
}

export interface EcommerceAdd {
    actionField: EcommerceActionField
    products: AnalyticsProduct[]
}

export interface EcommerceClick {
    actionField: EcommerceActionField
    products: AnalyticsProduct[]
}

export interface EcommerceImpressions {
    impressions: AnalyticsProduct[]
}

export interface EcommercePurchase {
    actionField: EcommerceActionField & {
        id: string
        revenue: number
    }
    products: AnalyticsProduct[]
}

// ========== GTM Event Types ==========

export interface BaseGtmEvent {
    event: string
    gtmUaEventCategory?: string
    gtmUaEventAction?: string
    gtmUaEventLabel?: string
    gtmUaEventValue?: number
    gtmUaEventNonInteraction?: string
}

export interface EcommerceEvent extends BaseGtmEvent {
    event: 'gtm-ee-event'
    gtmUaEventCategory: 'Enhanced Ecommerce'
    ecommerce: {
        detail?: EcommerceDetail
        add?: EcommerceAdd
        click?: EcommerceClick
        impressions?: AnalyticsProduct[]
        purchase?: EcommercePurchase
    }
}

export interface CustomDimensionsEvent extends BaseGtmEvent {
    dimension1?: string // Destination airport code
    dimension2?: string // Page type
    dimension3?: number // Product price
    dimension4?: string // Origin airport code
}

// ========== DataLayer Types ==========

export type DataLayerEvent = EcommerceEvent | CustomDimensionsEvent | BaseGtmEvent

export interface DataLayer extends Array<DataLayerEvent | Record<string, any>> {
    push(event: DataLayerEvent | Record<string, any>): void
}

// ========== GDPR Types ==========

export interface GdprSettings {
    isAccepted: boolean
    isEuropeanUser: boolean
    showBanner: boolean
}

// ========== Analytics Configuration ==========

export interface AnalyticsConfig {
    gtmId: string
    gaId?: string
    enableDebug: boolean
    enableGdpr: boolean
    loadDelay: number
}

// ========== Lead Types ==========

export interface Lead {
    id?: number
    slug: string
    data: string // JSON encoded BookingFormData
    created_at: number
    updated_at: number
    can_sync: boolean
    is_sync: boolean
    is_clean: boolean
    ip?: string
    mail_send_at?: number
}

// ========== Global Window Extensions ==========

declare global {
    interface Window {
        dataLayer: DataLayer
        gtag?: (...args: any[]) => void
        // Legacy support
        googleEvent?: {
            send: (data: any) => void
            sendDimensions: (product: AnalyticsProduct) => void
            data: any
            fromCode: string
            toCode: string
            itineraryClass: ItineraryClassMap
            itineraryType: ItineraryTypeMap
        }
    }
}

export {}
