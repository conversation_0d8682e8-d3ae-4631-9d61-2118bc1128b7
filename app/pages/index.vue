<template>
    <div class="bg-gray-100">
        <LayoutHeader />
        <HeroSection />
        <WelcomeSection />
        <CompanyDescriptionSection />
        <BookWithUsSection />
        <ContactUsSection />
        <ReviewsSection />
        <HowToBookSection />
        <BestDealsSection />
        <SubscribeSection />
        <BusinessAirlinesSection />
        <BestDealsListSection />
        <LayoutFooter />
    </div>
</template>

<script setup lang="ts">
import HeroSection from '~/sections/HeroSection.vue'
import WelcomeSection from '~/sections/WelcomeSection.vue'
import CompanyDescriptionSection from '~/sections/CompanyDescriptionSection.vue'
import BookWithUsSection from '~/sections/BookWithUsSection.vue'
import ContactUsSection from '~/sections/ContactUsSection.vue'
import ReviewsSection from '~/sections/ReviewsSection.vue'
import HowToBookSection from '~/sections/HowToBookSection.vue'
import BestDealsSection from '~/sections/BestDealsSection.vue'
import SubscribeSection from '~/sections/SubscribeSection.vue'
import BusinessAirlinesSection from '~/sections/BusinessAirlinesSection.vue'
import BestDealsListSection from '~/sections/BestDealsListSection.vue'
</script>

