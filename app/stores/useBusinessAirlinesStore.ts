import { defineStore } from 'pinia'
import type { ApiResponseType } from '~~/server/api/best-deals.get'

export const useBusinessAirlinesStore = defineStore('business-airlines', () => {
    const businessAirlines = ref<Partial<BestDeals>[]>()

    const fetchBusinessAirlines = async () => {
        const response = await $fetch('/api/business-airlines') as ApiResponseType<Partial<BestDeals>[]>
        businessAirlines.value = response.items
    }

    return {
        fetchBusinessAirlines,
        businessAirlines,
    }
})
