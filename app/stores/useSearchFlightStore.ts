import { defineStore, skipHydrate } from 'pinia'
import { FlightFormData, FlightRoute, TripClass, TripType } from '@tmg/flight-form'

export const useSearchFlightStore = defineStore('search-flight', () => {
    const formData = new FlightFormData({
        routes: [new FlightRoute()],
        tripType: TripType.OneWay,
        tripClass: TripClass.Business,
    })

    const flightFormData = reactive(formData)

    return {
        // Exclude from server hydration as it contains non-serializable objects
        flightFormData: skipHydrate(flightFormData),
    }
})
