import { defineStore, skipHydrate } from 'pinia'
import type { Aero, PlaceIdentification } from '@tmg/aero-data-sdk'
import { createAeroDataSdk } from '@tmg/aero-data-sdk'
import PlaceDictionary from '~/lib/Dictionary/PlaceDictionary'
import AirlineDictionary from '~/lib/Dictionary/AirlineDictionary'
import CountryDictionary from '~/lib/Dictionary/CountryDictionary'

export const useAeroDataStore = defineStore('aero-data', () => {
    const config = useRuntimeConfig()

    const aeroData = createAeroDataSdk({
        baseUrl: config.public.aeroData.apiBase,
        debug: config.public.debug,
        logger: useLogger('aero-data'),
    })

    const placesDictionary = new PlaceDictionary('places')

    async function searchPlaces(query: string): Promise<Aero.PlaceSuggestion[]> {
        if (query.length <= 1) {
            return []
        }

        const results: Aero.PlaceSuggestion[] = []

        if (query !== '') {
            const response = await aeroData.searchPlaces(query)

            for (const place of response) {
                results.push(place)
            }
        }

        return results || []
    }

    async function fetchPlacesInfo(placeIdentifications: PlaceIdentification[]) {
        const placesToFetch = placeIdentifications.filter((identification) => {
            return !placesDictionary.hasItem(identification)
        })

        if (!placesToFetch.length) {
            return
        }

        try {
            const result = await aeroData.fetchPlacesInfo(placesToFetch)

            addPlacesToDictionary(Object.values(result))
        } catch (error) {
            console.error('Failed to fetch places info', error)
        }

        for (const identification of placesToFetch) {
            if (!placesDictionary.hasItem(identification)) {
                addFakePlaceToDictionary(identification)
            }
        }
    }

    function addFakePlaceToDictionary(identification: PlaceIdentification) {
        addPlacesToDictionary([{
            type: identification.type ?? 'airport',
            code: identification.code,
            name: identification.code,
            main: 1,
            timezone: 'UTC',
            city: null,
            country: null,
            location: {
                lat: 0,
                lng: 0,
            },
            region: null,
            children: null,
        }])
    }

    function addPlacesToDictionary(places: Aero.Place[]) {
        placesDictionary.add(places)
    }

    function getPlaceInfo(placeIdentification: PlaceIdentification): Aero.Place {
        const info = placesDictionary.getItem(placeIdentification)

        if (!info) {
            useLogger('aero-data').fatal('Place info not found', placeIdentification, '. Fake info was not added to dictionary.')
        }

        return info!
    }

    const airlinesDictionary = new AirlineDictionary('airlines')

    function getAirlineInfo(airlineCode: string): Aero.Airline | undefined {
        return airlinesDictionary.get(airlineCode)
    }

    async function fetchAirlinesInfo(airlineCodes: string[]) {
        const codesToFetch = airlineCodes.filter((code) => {
            return !airlinesDictionary.has(code)
        })

        if (!codesToFetch.length) {
            return
        }

        try {
            const result = await aeroData.fetchAirlinesInfo(codesToFetch)

            addAirlinesToDictionary(Object.values(result))
        } catch (error) {
            console.error('Failed to fetch airlines info', error)
        }

        for (const code of codesToFetch) {
            if (!airlinesDictionary.has(code)) {
                addFakeAirlineToDictionary(code)
            }
        }
    }

    function addAirlinesToDictionary(airlines: Aero.Airline[]) {
        airlinesDictionary.add(airlines)
    }

    function addFakeAirlineToDictionary(airlineCode: string) {
        addAirlinesToDictionary([{
            code: airlineCode,
            name: airlineCode,
            image: getFakeAirlineImage(airlineCode),
        }])
    }

    function getAirlineImage(airlineCode: string, thumbnail?: Aero.AirlineImageThumbnail): Aero.Image {
        const info = getAirlineInfo(airlineCode)

        if (!info) {
            return getFakeAirlineImage(airlineCode, thumbnail)
        }

        return info.image
    }

    function getFakeAirlineImage(airlineCode: string, thumbnail?: Aero.AirlineImageThumbnail): Aero.Airline['image'] {
        let size = [40, 40]

        if (!['40x40', '80x80'].includes(thumbnail ?? '')) {
            size = thumbnail ? thumbnail.split('x').map(Number) : [40, 40]
        }

        const source = {
            src: `https://pics.avs.io/${size[0]}/${size[1]}/${airlineCode}.svg`,
            mime: 'image/svg+xml',
        }

        return {
            original: source,
            thumbnail: {
                '40x40': [source],
                '80x80': [source],
            },
        }
    }

    //

    const countryDictionary = new CountryDictionary('countries')

    let countriesFetched = !countryDictionary.isEmpty()

    async function fetchCountriesInfo(countryCodes?: string[]) {
        if (countriesFetched) {
            return
        }

        // Always fetch all countries
        const countries = await aeroData.fetchCountries()

        countryDictionary.add(countries)

        if (countryCodes) {
            for (const code of countryCodes) {
                if (!countryDictionary.hasItem({ code })) {
                    addFakeCountryToDictionary(code)
                }
            }
        }

        if (!countryCodes) {
            countriesFetched = true
        }
    }

    function addFakeCountryToDictionary(countryCode: string) {
        countryDictionary.add({
            code: countryCode,
            name: countryCode,
            region: null,
            continent: null,
            currency: null,
        })
    }

    function getCountryInfo(countryCode: string): Aero.Country {
        const info = countryDictionary.getItem({
            code: countryCode,
        })

        if (!info) {
            useLogger('aero-data').fatal('Country info not found', countryCode + ',', 'Fake info was not added to dictionary.')

            addFakeCountryToDictionary(countryCode)

            return getCountryInfo(countryCode)
        }

        return info
    }

    //

    return {
        fetchPlacesInfo,
        getPlaceInfo,
        addPlacesToDictionary,

        fetchAirlinesInfo,
        getAirlineInfo,
        getAirlineImage,
        addAirlinesToDictionary,

        fetchCountriesInfo,
        getCountryInfo,
        countryDictionary: skipHydrate(countryDictionary),
        searchPlaces,
    }
})
