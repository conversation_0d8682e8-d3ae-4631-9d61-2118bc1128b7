import { defineStore } from 'pinia'
import type {
    AnalyticsProduct,
    UtmParameters,
    GdprSettings,
    BookingFormData,
    ItineraryClass,
    ItineraryType,
} from '~/types/analytics'

interface AnalyticsState {
    // GDPR state
    gdpr: GdprSettings
    
    // UTM tracking state
    utm: UtmParameters
    utmTimestamp: number | null
    
    // Current user session
    session: {
        gaClientId: string | null
        deviceId: string | null
        sessionStart: number
        pageViews: number
    }
    
    // Current product/offer data
    currentProduct: AnalyticsProduct | null
    
    // Booking form data
    bookingData: Partial<BookingFormData>
    
    // Analytics readiness
    isInitialized: boolean
    isGtmLoaded: boolean
}

export const useAnalyticsStore = defineStore('analytics', {
    state: (): AnalyticsState => ({
        gdpr: {
            isAccepted: false,
            isEuropeanUser: false,
            showBanner: false,
        },
        
        utm: {},
        utmTimestamp: null,
        
        session: {
            gaClientId: null,
            deviceId: null,
            sessionStart: Date.now(),
            pageViews: 0,
        },
        
        currentProduct: null,
        bookingData: {},
        
        isInitialized: false,
        isGtmLoaded: false,
    }),

    getters: {
        /**
         * Check if analytics can be used
         */
        canUseAnalytics: (state) => {
            return state.gdpr.isAccepted && state.isGtmLoaded
        },

        /**
         * Check if UTM data is fresh (less than 24 hours)
         */
        isUtmDataFresh: (state) => {
            if (!state.utmTimestamp) return false
            const age = Date.now() - state.utmTimestamp
            return age < 24 * 60 * 60 * 1000 // 24 hours
        },

        /**
         * Get current session duration
         */
        sessionDuration: (state) => {
            return Date.now() - state.session.sessionStart
        },

        /**
         * Check if user has UTM parameters
         */
        hasUtmData: (state) => {
            return Object.keys(state.utm).length > 0
        },
    },

    actions: {
        /**
         * Initialize analytics store
         */
        async initialize() {
            if (this.isInitialized) return

            try {
                // Initialize composables
                const { gdprSettings, canLoadAnalytics } = useGdprCompliance()
                const { utmData } = useUtmTracking()
                const { getGaClientId, getDeviceId, isGtmLoaded } = useGoogleAnalytics()

                // Watch for changes
                watch(gdprSettings, (newSettings) => {
                    this.gdpr = { ...newSettings }
                }, { deep: true, immediate: true })

                watch(utmData, (newUtm) => {
                    this.utm = { ...newUtm }
                    this.utmTimestamp = Date.now()
                }, { deep: true, immediate: true })

                watch(isGtmLoaded, (loaded) => {
                    this.isGtmLoaded = loaded
                }, { immediate: true })

                // Update session data
                if (import.meta.client) {
                    this.session.gaClientId = getGaClientId()
                    this.session.deviceId = getDeviceId()
                }

                this.isInitialized = true
            } catch (error) {
                console.error('[Analytics Store] Initialization error:', error)
            }
        },

        /**
         * Update GDPR settings
         */
        updateGdpr(settings: Partial<GdprSettings>) {
            this.gdpr = { ...this.gdpr, ...settings }
        },

        /**
         * Accept GDPR consent
         */
        acceptGdpr() {
            const { acceptConsent } = useGdprCompliance()
            acceptConsent()
        },

        /**
         * Reject GDPR consent
         */
        rejectGdpr() {
            const { rejectConsent } = useGdprCompliance()
            rejectConsent()
        },

        /**
         * Update UTM data
         */
        updateUtm(utm: UtmParameters) {
            this.utm = { ...utm }
            this.utmTimestamp = Date.now()
        },

        /**
         * Clear UTM data
         */
        clearUtm() {
            this.utm = {}
            this.utmTimestamp = null
        },

        /**
         * Set current product for tracking
         */
        setCurrentProduct(product: AnalyticsProduct) {
            this.currentProduct = product
        },

        /**
         * Clear current product
         */
        clearCurrentProduct() {
            this.currentProduct = null
        },

        /**
         * Update booking form data
         */
        updateBookingData(data: Partial<BookingFormData>) {
            this.bookingData = { ...this.bookingData, ...data }
        },

        /**
         * Clear booking data
         */
        clearBookingData() {
            this.bookingData = {}
        },

        /**
         * Track page view
         */
        trackPageView() {
            this.session.pageViews++
        },

        /**
         * Track product detail view
         */
        async trackProductDetail(
            routeName: string,
            routeId: string,
            price: number,
            tripType: ItineraryType,
            serviceClass: ItineraryClass
        ) {
            if (!this.canUseAnalytics) return

            const { trackProductDetail } = useEcommerce()
            const { sendDimensions, createProduct } = useGoogleAnalytics()

            // Create and store product
            const product = createProduct(routeName, routeId, price, tripType, serviceClass)
            this.setCurrentProduct(product)

            // Track the event
            await trackProductDetail(routeName, routeId, price, tripType, serviceClass)
            
            // Send custom dimensions
            sendDimensions(product)
        },

        /**
         * Track add to cart
         */
        async trackAddToCart(
            routeName: string,
            routeId: string,
            price: number,
            tripType: ItineraryType,
            serviceClass: ItineraryClass,
            quantity: number = 1
        ) {
            if (!this.canUseAnalytics) return

            const { trackAddToCart } = useEcommerce()
            await trackAddToCart(routeName, routeId, price, tripType, serviceClass, quantity)
        },

        /**
         * Track product click
         */
        async trackProductClick(
            routeName: string,
            routeId: string,
            price: number,
            tripType: ItineraryType,
            serviceClass: ItineraryClass
        ) {
            if (!this.canUseAnalytics) return

            const { trackProductClick } = useEcommerce()
            await trackProductClick(routeName, routeId, price, tripType, serviceClass)
        },

        /**
         * Reset store state
         */
        reset() {
            this.$reset()
        },
    },

    persist: {
        storage: persistedState.localStorage,
        paths: ['utm', 'utmTimestamp', 'session.pageViews'],
    },
})
