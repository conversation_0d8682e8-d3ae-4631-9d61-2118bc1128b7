import { defineStore } from 'pinia'
import type { ApiResponseType } from '~~/server/api/best-deals.get'

export enum BestDealType {
    Region = 'region',
    Country = 'country',
    City = 'city',
    Airline = 'airline',
}

export type BestDealsListType = {
    group: string,
    values: {
        title: string,
        url: string
    }[]
}

export const useBestDealsListStore = defineStore('best-deals-list', () => {
    const bestDealsList = ref<BestDealsListType[]>()

    const fetchBestDealsList = async () => {
        const response = await $fetch('/api/best-deals-list') as ApiResponseType<BestDealsListType[]>
        bestDealsList.value = response.items
    }

    return {
        fetchBestDealsList,
        bestDealsList,
    }
})
