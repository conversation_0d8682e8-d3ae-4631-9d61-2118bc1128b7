import { useDictionaryStorage } from '~/composables/useDictionaryStorage'
import { z } from 'zod'
import { ensureArray } from '~/helpers/ArrayHelper'

export default abstract class Dictionary<TIdentification, TItem extends TIdentification> {
    protected version = 'v1'

    protected storage = shallowReactive(new Map<string, TItem>())

    declare protected cacheStorage: {
        get(key: string): TItem | undefined,
        getAll(): Map<string, TItem>,
        set(pairs: [key: string, item: TItem][]): void,
    }

    protected isDebug = useRuntimeConfig().public.debug

    public constructor(protected storageKey: string) {
        this.cacheStorage = useDictionaryStorage<TItem>(`${this.version}:dictionary:${this.storageKey}`)

        this.restoreFromCache()
    }

    public restoreFromCache() {
        const cache = this.cacheStorage.getAll()

        if (!this.isValidData(cache)) {
            useLogger('dictionary-storage').warn(`Invalid cache data: dictionary:${this.storageKey}`)

            return
        }

        for (const [key, item] of cache) {
            this.storage.set(key, item)
        }

        if (this.isDebug && cache.size > 0) {
            useLogger('dictionary-storage').log(`Restored from cache: dictionary:${this.storageKey}`, `${this.storage.size} items`)
        }
    }

    protected getDataSchema(): z.ZodSchema<unknown> | undefined {
        return
    }

    protected isValidData(data: unknown): boolean {
        try {
            const schema = this.getDataSchema()

            if (!schema) {
                return true
            }

            z.map(z.string(), schema).parse(data)

            return true
        } catch (e) {
            useLogger('dictionary-storage').error(`Invalid data: ${this.storageKey}`, e)

            return false
        }
    }

    public isEmpty(): boolean {
        return this.storage.size === 0
    }

    protected writeToCache(pairs: [key: string, item: TItem][]) {
        this.cacheStorage.set(pairs)
    }

    public has(key: string): boolean {
        return this.storage.has(key)
    }

    public hasItem(item: TIdentification): boolean {
        const key = this.getKey(item)

        return this.has(key)
    }

    public get(key: string): TItem | undefined {
        return this.storage.get(key)
    }

    public getItem(item: TIdentification): TItem | undefined {
        const key = this.getKey(item)

        return this.get(key)
    }

    protected abstract getKey(item: TIdentification): string

    public add(items: TItem | TItem[]) {
        const item = ensureArray(items)

        const pairs: [key: string, item: TItem][] = item.map(i => [this.getKey(i), i])

        for (const [key, i] of pairs) {
            this.storage.set(key, i)
        }

        // noinspection JSIgnoredPromiseFromCall
        this.writeToCache(pairs)
    }

    public all() {
        return Array.from(this.storage.values())
    }
}
