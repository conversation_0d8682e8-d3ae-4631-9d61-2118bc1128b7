import type { Aero, PlaceIdentification } from '@tmg/aero-data-sdk'
import { ensureArray } from '~/helpers/ArrayHelper'

type TItem = Aero.Place
type TPlaceDictionaryItem = {
    [key in Aero.TypeOfPlace | 'unknown']?: TItem
}

export default class PlaceDictionary {
    protected version = 'v1'

    protected storage = shallowReactive(new Map<string, TPlaceDictionaryItem>())

    declare protected cacheStorage: {
        get(key: string): TPlaceDictionaryItem | undefined,
        getAll(): Map<string, TPlaceDictionaryItem>,
        set(pairs: [key: string, item: TPlaceDictionaryItem][]): void,
    }

    protected isDebug = useRuntimeConfig().public.debug

    public constructor(protected storageKey: string) {
        this.cacheStorage = useDictionaryStorage<TPlaceDictionaryItem>(`${this.version}:dictionary:${this.storageKey}`)

        this.restoreFromCache()
    }

    public restoreFromCache() {
        const cache = this.cacheStorage.getAll()

        for (const [key, item] of cache) {
            this.storage.set(key, item)
        }

        if (this.isDebug && cache.size > 0) {
            useLogger('dictionary-storage').log(`Restored from cache: dictionary:${this.storageKey}`, `${this.storage.size} items`)
        }
    }

    public isEmpty(): boolean {
        return this.storage.size === 0
    }

    protected writeToCache(pairs: [key: string, item: TPlaceDictionaryItem][]) {
        this.cacheStorage.set(pairs)
    }

    public has(key: string): boolean {
        return this.storage.has(key)
    }

    public hasItem(item: PlaceIdentification): boolean {
        const data = this.storage.get(item.code)

        if (!data) {
            return false
        }

        return item.type === undefined || data[item.type] !== undefined
    }

    public getItem(item: PlaceIdentification): TItem | undefined {
        const data = this.storage.get(item.code)

        if (!data) {
            return
        }

        return item.type === undefined ? data[Object.keys(data)[0] as keyof typeof data] : data[item.type]
    }

    public add(items: TItem | TItem[]) {
        const item = ensureArray(items)

        const pairs: [key: string, item: TPlaceDictionaryItem][] = item.map(item => {
            const key = item.code
            const normalizedItem = this.storage.get(key) || {} as TPlaceDictionaryItem

            normalizedItem[item.type] = item

            return [key, normalizedItem]
        })

        for (const [key, i] of pairs) {
            this.storage.set(key, i)
        }

        // noinspection JSIgnoredPromiseFromCall
        this.writeToCache(pairs)
    }

    public all() {
        return Array.from(this.storage.values())
    }
}
