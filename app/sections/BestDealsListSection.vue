<template>
    <div class="container deals-section">
        <div class="deals-section__list">
            <div class="deals-section__list__group">
                <div
                    v-if="bestDealsList && bestDealsList.length > 0"
                    class="deals-section__list__group__container"
                >
                    <div
                        v-for="(item, index) in bestDealsList"
                        :key="index"
                        class="deals-section__group-item"
                    >
                        <button
                            class="deals-section__group-item__accordion"
                            @click="toggle(index)"
                        >
                            {{ item.group }}
                            <IconChevron :class="{ 'rotate-180': isOpen(index) }" />
                        </button>
                        <div
                            class="deals-section__group-item__container"
                            :class="{
                                'deals-section__group-item__container--opened': isOpen(index)
                            }"
                        >
                            <div class="deals-section__group-item__container__content">
                                <h3 class="deals-section__group-item__container__content__heading text-gradient">
                                    {{ item.group }}
                                </h3>
                                <ul class="deals-section__group-item__container__content__list">
                                    <li
                                        v-for="(listItem, listIndex) in item.values"
                                        :key="listIndex"
                                        class="deals-section__group-item__container__content__list__item"
                                    >
                                        <a :href="listItem.url">{{ listItem.title }}</a>
                                    </li>
                                </ul>
                                <div v-if="item.group !== BestDealType.Region" class="deals-section__group-item__container__content__group">
                                    <a class="deals-section__group-item__container__content__group__title">
                                        View more
                                        <IconChevron class="w-3 h-3 ml-2 -mt-px stroke-current -rotate-90" />
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { BestDealType, useBestDealsListStore } from '~/stores/useBestDealsListStore'

const bestDealsListStore = useBestDealsListStore()

const bestDealsList = computed(() => {
    return bestDealsListStore.bestDealsList
})

// Use await to ensure data is loaded on both server and client
await bestDealsListStore.fetchBestDealsList()
//

const openedIndexes = ref<Set<number>>(new Set())

function toggle(index: number) {
    if (openedIndexes.value.has(index)) {
        openedIndexes.value.delete(index)
    } else {
        openedIndexes.value.add(index)
    }
}

function isOpen(index: number) {
    return openedIndexes.value.has(index)
}
</script>
