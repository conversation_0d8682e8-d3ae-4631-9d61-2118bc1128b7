<template>
    <div class="section">
        <span class="section__headline">Fresh</span>
        <span class="section__title">Best deals</span>
        <div class="best-deals">
            <div
                v-if="bestDealRecords && bestDealRecords.length > 0"
                class="best-deals__items-container"
            >
                <Card
                    v-for="(deal, index) in bestDealRecords"
                    :key="index"
                    :card-type="BestDealType.Country"
                    :data="deal"
                />
            </div>

            <div class="best-deals__view-more">
                <a class="button best-deals__view-more__link">
                    <div class="relative w-full">
                        <span>View more</span>
                        <IconChevron class="best-deals__view-more__link__chevron" />
                    </div>
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Card from '~/components/Section/Card/Card.vue'
import { BestDealType } from '~/stores/useBestDealsListStore'

const bestDealsStore = useBestDealsStore()

const bestDealRecords = computed(() => {
    return bestDealsStore.bestDeals
})

// Use await to ensure data is loaded on both server and client
await bestDealsStore.fetchBestDeals()
</script>
