<template>
    <section class="section-hero">
        <AppVideo
            class="section-hero__video"
            src="video/background.mp4"
            :autoplay="true"
            :muted="true"
            :controls="false"
            :loop="true"
        />
        <div class="section-hero__overlay" />
        <FlightForm />
    </section>
</template>

<script setup lang="ts">
import { useMenuItems } from '~/composables/useMenuItems'
import { useMenuStore } from '~/stores/useMenuStore'
import AppVideo from '~/components/App/AppVideo.vue'
import 'assets/css/sections/hero.pcss'
import FlightForm from '~/components/Section/FlightForm/FlightForm.vue'

defineOptions({
    name: 'HeroSection',
})

// Setup menu functionality
const menu = useMenuStore()

function openMenu() {
    menu.open()
}

const { menuItems } = useMenuItems()
</script>
