<template>
    <div class="section">
        <span class="section__headline">Best</span>
        <span class="section__title">Business class airlines</span>
        <div class="best-deals">
            <div
                v-if="businessAirlinesRecords && businessAirlinesRecords.length > 0"
                class="best-deals__items-container"
            >
                <Card
                    v-for="(deal, index) in businessAirlinesRecords"
                    :key="index"
                    :card-type="BestDealType.Airline"
                    :data="deal"
                />
            </div>

            <div class="best-deals__view-more">
                <a class="button best-deals__view-more__link">
                    <div class="relative w-full">
                        <span>View more</span>
                        <IconChevron class="best-deals__view-more__link__chevron" />
                    </div>
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Card from '~/components/Section/Card/Card.vue'
import { useBusinessAirlinesStore } from '~/stores/useBusinessAirlinesStore'
import { BestDealType } from '~/stores/useBestDealsListStore'

const businessAirlinesStore = useBusinessAirlinesStore()

const businessAirlinesRecords = computed(() => {
    return businessAirlinesStore.businessAirlines
})

// Use await to ensure data is loaded on both server and client
await businessAirlinesStore.fetchBusinessAirlines()
</script>
