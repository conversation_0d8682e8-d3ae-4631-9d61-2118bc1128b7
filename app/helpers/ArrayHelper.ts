export function unique<T>(arr: T[]): T[] {
    return Array.from(new Set(arr))
}

export function ensureArray<T>(value: T | T[]): T[] {
    return Array.isArray(value) ? value : [value]
}

export function arraysAreEqual<T>(array1: T[], array2: T[]) {
    return array1.length === array2.length && array1.every((value, index) => value === array2[index])
}

type ObjectKey = string | number | symbol

export function groupBy<T, K extends keyof T>(list: T[], keyGetter: K): Record<T[K] extends ObjectKey ? T[K] : ObjectKey, T[]>
export function groupBy<T, K extends (value: T) => ObjectKey>(list: T[], keyGetter: K): Record<ReturnType<K>, T[]>
export function groupBy<T>(list: T[], keyGetter: keyof T | ((value: T) => ObjectKey)): Record<ObjectKey, T[]> {
    const map: any = {}

    for (const item of list) {
        const key = typeof keyGetter === 'function' ? keyGetter(item) : item[keyGetter]

        if (map[key] === undefined) {
            map[key] = [item]
        } else {
            map[key].push(item)
        }
    }

    return map
}
