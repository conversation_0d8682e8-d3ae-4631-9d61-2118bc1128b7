export function getDuration(seconds: number) {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds - hours * 3600) / 60)

    if (hours === 0) {
        return `${minutes}m`
    }

    return `${hours}h ${minutes}m`
}

/**
 * Returns date in format "Fri, Oct 10"
 */
export function formatDateWithoutYear(date: string | Date) {
    return new Intl.DateTimeFormat('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        timeZone: 'UTC',
    }).format(new Date(date))
}

export function formatTime(time: string) {
    return new Intl.DateTimeFormat('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        timeZone: 'UTC',
    }).format(new Date(time))
}

export function fromUnixTimestamp(timestamp: number | undefined | null): Date | undefined {
    if (!timestamp) {
        return
    }

    return new Date(timestamp * 1000)
}

export function toUnixTimestamp(date: Date | undefined | null): number | undefined {
    if (!date) {
        return
    }

    return Math.floor(date.getTime() / 1000)
}
