export function isSameValue(a: unknown, b: unknown): boolean {
    return a === b || JSON.stringify(a) === JSON.stringify(b)
}

export function ensureEnum<T>(value: string, enumType: Record<string, T>): T {
    if (!Object.values(enumType).includes(value as T)) {
        throw new Error(`Invalid enum value: ${value}`)
    }

    return value as T
}

export function isEmpty(value: unknown): boolean {
    return value === undefined || value === null || value === '' || (Array.isArray(value) && value.length === 0)
}

export function nanToUndefined(value: number): number | undefined {
    return isNaN(value) ? undefined : value
}

export function removeUndefined<T extends Record<string, any>>(obj: T): {
    [K in keyof T]-?: T[K]
} {
    const newObj: any = {}

    for (const key in obj) {
        if (obj[key] !== undefined) {
            newObj[key] = obj[key]
        }
    }

    return newObj
}

export type AssertObjectEqual<Expected, T extends Expected> = T

export type Awaitable<T> = T | Promise<T>

/**
 Matches any [primitive value](https://developer.mozilla.org/en-US/docs/Glossary/Primitive).
 */
export type Primitive =
    | null
    | undefined
    | string
    | number
    | boolean
    | symbol
    | bigint;

export type PartialDeep<T> = T extends Primitive
    ? Partial<T>
    : T extends object
        ? PartialObjectDeep<T>
        : unknown;

/**
 Same as `PartialDeep`, but accepts only `object`s as inputs. Internal helper for `PartialDeep`.
 */
type PartialObjectDeep<ObjectType extends object> = {
    [KeyType in keyof ObjectType]?: PartialDeep<ObjectType[KeyType]>
};

//

/**
 * Trims a string if it's a string type, otherwise returns the original value
 */
export function trimString(value: unknown): unknown {
    if (typeof value === 'string') {
        return value.trim()
    }

    return value
}

/**
 * Checks if a string contains only whitespace
 */
export function isWhitespaceOnly(value: unknown): boolean {
    return typeof value === 'string' && value.trim() === '' && value !== ''
}

/**
 * Recursively trims all string values in an object or array
 */
export function trimStringsInObject<T>(obj: T): T {
    if (obj === null || obj === undefined) {
        return obj
    }

    // Handle arrays
    if (Array.isArray(obj)) {
        return obj.map(item => trimStringsInObject(item)) as unknown as T
    }

    // Handle primitive string
    if (typeof obj === 'string') {
        return trimString(obj) as unknown as T
    }

    // Handle objects
    if (typeof obj === 'object') {
        if ((obj as any).constructor?.name) {
            return obj as T
        }

        obj = toRaw(obj)

        const result: Record<string, unknown> = {}
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                result[key] = trimStringsInObject((obj as Record<string, unknown>)[key])
            }
        }

        return result as T
    }

    // Return non-string primitives as is
    return obj
}
