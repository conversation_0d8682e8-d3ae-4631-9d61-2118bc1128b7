type FormatMoneyOptions = {
    fraction?: number
    grouping?: boolean
}

export function formatMoney(price: { amount: number, currency: string }, options?: FormatMoneyOptions): string
export function formatMoney(price: number, currency?: string, options?: FormatMoneyOptions): string
export function formatMoney(var1: any, var2?: any, var3?: any): string {
    const [amount, currency, options] = typeof var1 === 'number'
        ? [var1, var2 as string, var3 as FormatMoneyOptions]
        : [var1.amount, var1.currency, var2 as FormatMoneyOptions]

    const useFraction = options?.fraction ?? 2

    const formatter = new Intl.NumberFormat('en-US', {
        useGrouping: options?.grouping ?? false,
        minimumFractionDigits: useFraction,
        maximumFractionDigits: useFraction,
    })

    let result = formatter.format(amount)

    if (currency) {
        result = `$${result}`
    }

    return result
}

/**
 * Safe sum of money values
 */
export function getMoneySum(...prices: number[]): number {
    return normalizeNumberToMoney(prices.reduce((sum, price) => sum + price, 0) + Number.EPSILON)
}

export function getMoneyDifference(price1: number, price2: number): number {
    return normalizeNumberToMoney(price1 - price2)
}

export function normalizeNumberToMoney(price: number): number {
    return Math.round((price + Number.EPSILON) * 1e10) / 1e10
}

export function truncateTo(n: number, decimals: number): number {
    const factor = Math.pow(10, decimals)

    return Math.trunc(n * factor) / factor
}
