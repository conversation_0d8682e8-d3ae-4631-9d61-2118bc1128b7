export default defineNuxtPlugin(() => {
    // Client-side only
    if (import.meta.server) { return }

    const config = useRuntimeConfig()
    const jivoConfig = config.public.jivo

    // Check if Jivo is enabled in configuration
    if (!jivoConfig.enabled) {
        console.log('Jivo chat disabled in configuration')

        return
    }

    // Check if chat is disabled via cookie
    const isJivoDisabled = () => {
        if (typeof document === 'undefined') { return true }

        return document.cookie.includes('dJ=')
    }

    // Function to load Jivo script
    const loadJivoScript = () => {
        if (isJivoDisabled()) {
            console.log('Jivo chat disabled by cookie')

            return
        }

        // Check if script is already loaded
        if (document.querySelector('script[src*="jivosite.com"]')) {
            console.log('Jivo script already loaded')

            return
        }

        const script = document.createElement('script')
        script.async = true
        script.src = `//code-eu1.jivosite.com/widget/${jivoConfig.widgetId}`

        script.onload = () => {
            console.log('Jivo chat loaded successfully')
        }

        script.onerror = () => {
            console.error('Failed to load Jivo chat')
        }

        document.body.appendChild(script)
    }

    // Load script with delay
    setTimeout(() => {
        loadJivoScript()
    }, jivoConfig.loadDelay || 5000)

    // Provide global functions for working with Jivo
    return {
        provide: {
            jivo: {
                // Function to programmatically open callback button
                openCallbackButton: () => {
                    if (import.meta.server) { return }

                    const jivoCallbackBtn = document.querySelector('jdiv.__jivoCallbackBtn') as HTMLElement

                    if (jivoCallbackBtn) {
                        jivoCallbackBtn.click()
                    } else {
                        console.warn('Jivo callback button not found')
                    }
                },

                // Function to disable chat (set cookie)
                disable: () => {
                    if (import.meta.server) { return }

                    document.cookie = 'dJ=1; path=/; max-age=31536000' // 1 year
                    console.log('Jivo chat disabled')
                },

                // Function to enable chat (remove cookie)
                enable: () => {
                    if (import.meta.server) { return }

                    document.cookie = 'dJ=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
                    console.log('Jivo chat enabled')
                    loadJivoScript()
                },

                // Status check
                isDisabled: isJivoDisabled,
            },
        },
    }
})

// Global function for callback
declare global {
    interface Window {
        jivo_onLoadCallback?: () => void
    }
}
