export default defineNuxtPlugin((nuxt) => {
    const site =  {
        title: 'Travel Business Class',
        abbreviation: 'TBC',
        logoText: 'TravelBusinessClass.com',
        domain: 'travelbusinessclass.com',
        email: '<EMAIL>',
        phone: '+****************',
        call: '',
        address: '323 Sunny Isles Boulevard, Suite 700, Sunny Isles Beach, FL, 33160',
        legalName: 'Travel Management Group LLC',
        legalTerm: 'TravelBusinessClass',
        googleScore: '4.7',
        trustpilotScore: '4.9',
        trustpilotTotalReviews: '1,590',
    }

    site.call = `tel:+${site.phone.replace(/\D/g, '')}`

    nuxt.vueApp.config.globalProperties.$site = site

    return {
        provide: {
            site,
        },
    }
})

declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $site: {
            title: string,
            abbreviation: string,
            logoText: string,
            domain: string,
            email: string,
            phone: string,
            call: string,
            address: string,
            legalName: string,
            legalTerm: string,
            googleScore: string,
            trustpilotScore: string,
            trustpilotTotalReviews: string,
        },
    }
}
