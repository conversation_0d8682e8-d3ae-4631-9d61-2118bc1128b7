/**
 * Analytics Plugin for TravelBusinessClass
 * Initializes Google Analytics, UTM tracking, and GDPR compliance
 */
export default defineNuxtPlugin(async (nuxtApp) => {
    const config = useRuntimeConfig()
    
    // Only initialize on client-side
    if (import.meta.server) {
        return
    }

    // Initialize GDPR compliance first
    const { initializeGdpr, canLoadAnalytics } = useGdprCompliance()
    
    // Initialize UTM tracking
    const { initializeUtmTracking } = useUtmTracking()
    
    // Initialize Google Analytics
    const { initializeDataLayer, waitForGtm } = useGoogleAnalytics()

    try {
        // Start GDPR check
        await initializeGdpr()
        
        // Initialize UTM tracking regardless of GDPR (for internal analytics)
        initializeUtmTracking()
        
        // Initialize dataLayer
        initializeDataLayer()

        // Wait for GDPR consent before loading GTM
        const checkConsent = async () => {
            if (canLoadAnalytics.value) {
                // GTM should be loaded by @nuxt/scripts module
                // We just need to wait for it to be ready
                const gtmReady = await waitForGtm(10000)
                
                if (gtmReady) {
                    if (config.public.debug) {
                        console.log('[Analytics] GTM loaded and ready')
                    }
                } else {
                    console.warn('[Analytics] GTM failed to load within timeout')
                }
                
                return
            }

            // If consent not given yet, wait and check again
            setTimeout(checkConsent, 1000)
        }

        // Start consent checking
        checkConsent()

        // Listen for GDPR events
        window.addEventListener('gdpr:accepted', () => {
            if (config.public.debug) {
                console.log('[Analytics] GDPR accepted, analytics enabled')
            }
            checkConsent()
        })

        window.addEventListener('gdpr:rejected', () => {
            if (config.public.debug) {
                console.log('[Analytics] GDPR rejected, analytics disabled')
            }
        })

        if (config.public.debug) {
            console.log('[Analytics] Plugin initialized')
        }

    } catch (error) {
        console.error('[Analytics] Plugin initialization error:', error)
    }

    // Provide global analytics methods
    return {
        provide: {
            analytics: {
                // Legacy support for old googleEvent object
                googleEvent: {
                    send: (data: any) => {
                        const { sendEvent } = useGoogleAnalytics()
                        sendEvent(data)
                    },
                    sendDimensions: (product: any) => {
                        const { sendDimensions } = useGoogleAnalytics()
                        sendDimensions(product)
                    },
                    data: {},
                    fromCode: '',
                    toCode: '',
                    itineraryClass: {
                        premium: 'Premium Class',
                        business: 'Business Class',
                        first: 'First Class',
                    },
                    itineraryType: {
                        oneWay: 'One Way',
                        roundTrip: 'Round Trip',
                        multi: 'Multi City',
                    },
                },
                
                // New composable-based API
                useGoogleAnalytics,
                useEcommerce,
                useUtmTracking,
                useGdprCompliance,
            },
        },
    }
})
