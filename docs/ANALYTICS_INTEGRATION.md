# Google Analytics Integration для TravelBusinessClass

## Обзор

Новая система аналитики для TravelBusinessClass построена на Nuxt 3 с использованием композаблов и полной поддержкой SSR. Система включает:

- **Google Tag Manager** интеграцию через @nuxt/scripts
- **Enhanced Ecommerce** отслеживание
- **UTM параметры** отслеживание и сохранение
- **GDPR соответствие** с автоматическим определением региона
- **TypeScript** типизацию для всех компонентов

## Быстрый старт

### 1. Настройка переменных окружения

Создайте `.env` файл с необходимыми переменными:

```env
# Google Tag Manager ID
NUXT_PUBLIC_ANALYTICS_GTM_ID=GTM-XXXXXXX

# Google Analytics ID (опционально)
NUXT_PUBLIC_ANALYTICS_GA_ID=GA-XXXXXXX

# Включить отладку
NUXT_PUBLIC_DEBUG=true
```

### 2. Добавление GDPR баннера

Добавьте компонент GDPR баннера в ваш layout:

```vue
<template>
  <div>
    <!-- Ваш контент -->
    <NuxtPage />

    <!-- GDPR Banner -->
    <AnalyticsGdprBanner />
  </div>
</template>
```

### 3. Базовое использование

```vue
<script setup>
// Импорт композаблов
const { trackProductDetail } = useEcommerce()
const { canLoadAnalytics } = useGdprCompliance()

// Отслеживание просмотра продукта
const trackProduct = async () => {
  if (!canLoadAnalytics.value) return

  await trackProductDetail(
    'JFK - LHR',      // Название маршрута
    'JFK2LHR',        // ID маршрута
    6000.66,          // Цена
    'roundTrip',      // Тип поездки
    'premium'         // Класс обслуживания
  )
}
</script>
```

## Композаблы

### useGoogleAnalytics

Основной композабл для работы с Google Analytics.

```typescript
const {
  // Методы
  sendEvent,           // Отправка событий в dataLayer
  sendEventSafe,       // Отправка с проверкой загрузки GTM
  sendDimensions,      // Отправка пользовательских измерений
  createProduct,       // Создание объекта продукта

  // Состояние
  isGtmLoaded,         // Статус загрузки GTM

  // Утилиты
  getGaClientId,       // Получение GA Client ID
  getDeviceId,         // Получение Device ID
  waitForGtm,          // Ожидание загрузки GTM
} = useGoogleAnalytics()
```

### useEcommerce

Композабл для Enhanced Ecommerce событий.

```typescript
const {
  trackProductDetail,     // Просмотр деталей продукта
  trackAddToCart,         // Добавление в корзину
  trackProductClick,      // Клик по продукту
  trackProductImpressions,// Показы продуктов
  trackPurchase,          // Покупка
  trackProductList,       // Список продуктов
  trackClassChange,       // Смена класса обслуживания
} = useEcommerce()
```

### useUtmTracking

Композабл для отслеживания UTM параметров.

```typescript
const {
  // Данные
  utmData,              // Текущие UTM параметры
  isGoogleAds,          // Флаг Google Ads трафика
  hasUtmParameters,     // Наличие UTM параметров

  // Методы
  getCurrentUtm,        // Получение текущих UTM
  saveUtmVisit,         // Сохранение визита
  appendUtmToUrl,       // Добавление UTM к URL
  clearUtmData,         // Очистка UTM данных
} = useUtmTracking()
```

### useGdprCompliance

Композабл для соблюдения GDPR.

```typescript
const {
  // Состояние
  showBanner,           // Показывать ли баннер
  isEuropeanUser,       // Европейский пользователь
  isAccepted,           // Согласие дано
  canLoadAnalytics,     // Можно ли загружать аналитику

  // Действия
  acceptConsent,        // Принять согласие
  rejectConsent,        // Отклонить согласие
  resetGdpr,            // Сбросить настройки
} = useGdprCompliance()
```

## Pinia Store

Для управления состоянием аналитики используется Pinia store:

```typescript
const analyticsStore = useAnalyticsStore()

// Инициализация
await analyticsStore.initialize()

// Отслеживание событий через store
await analyticsStore.trackProductDetail(
  'JFK - LHR',
  'JFK2LHR',
  6000.66,
  'roundTrip',
  'premium'
)

// Управление GDPR
analyticsStore.acceptGdpr()
analyticsStore.rejectGdpr()
```

## Примеры использования

### Отслеживание просмотра страницы предложения

```vue
<script setup>
const route = useRoute()
const { trackProductDetail } = useEcommerce()

// Данные предложения
const offer = {
  name: 'JFK - LHR',
  id: 'JFK2LHR',
  price: 6000.66,
  tripType: 'roundTrip',
  serviceClass: 'premium'
}

// Отслеживание при загрузке страницы
onMounted(async () => {
  await trackProductDetail(
    offer.name,
    offer.id,
    offer.price,
    offer.tripType,
    offer.serviceClass
  )
})
</script>
```

### Отслеживание добавления в корзину

```vue
<script setup>
const { trackAddToCart } = useEcommerce()

const addToCart = async (offer) => {
  // Отслеживание события
  await trackAddToCart(
    offer.name,
    offer.id,
    offer.price,
    offer.tripType,
    offer.serviceClass,
    1 // количество
  )

  // Ваша логика добавления в корзину
  // ...
}
</script>
```

### Отслеживание смены класса обслуживания

```vue
<script setup>
const { trackClassChange } = useEcommerce()

const changeServiceClass = async (newClass, newPrice) => {
  await trackClassChange(
    offer.name,
    offer.id,
    newPrice,
    offer.tripType,
    newClass
  )

  // Обновление UI
  // ...
}
</script>
```

## API Endpoints

### POST /api/utm/visit

Сохранение данных UTM визита.

```typescript
// Тело запроса
interface UtmVisit {
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
  utm_content?: string
  utm_term?: string
  gclid?: string
  gbraid?: string
  gad_source?: string
  ga?: string    // GA Client ID
  did?: string   // Device ID
  referer?: string
  url?: string
  ip?: string
  ua?: string
  created_at?: number
}
```

## Конфигурация

### nuxt.config.ts

```typescript
export default defineNuxtConfig({
  runtimeConfig: {
    public: {
      analytics: {
        gtmId: process.env.NUXT_PUBLIC_ANALYTICS_GTM_ID,
        gaId: process.env.NUXT_PUBLIC_ANALYTICS_GA_ID,
        enableDebug: true,
        enableGdpr: true,
        enableGdprApiCheck: false,
        loadDelay: 5000,
      },
    },
  },

  scripts: {
    registry: {
      googleTagManager: {
        id: process.env.NUXT_PUBLIC_ANALYTICS_GTM_ID,
        delay: 5000,
        consent: 'granted',
      },
    },
  },
})
```

## Миграция со старой системы

### Замена старого googleEvent объекта

Старый код:
```javascript
window.googleEvent.send({
  event: 'custom_event',
  // ...
})
```

Новый код:
```typescript
const { sendEvent } = useGoogleAnalytics()
sendEvent({
  event: 'custom_event',
  // ...
})
```

### Обратная совместимость

Плагин предоставляет объект `$analytics.googleEvent` для обратной совместимости:

```vue
<script setup>
const { $analytics } = useNuxtApp()

// Использование старого API (не рекомендуется)
$analytics.googleEvent.send(eventData)

// Рекомендуемый подход
const { sendEvent } = useGoogleAnalytics()
sendEvent(eventData)
</script>
```

## Отладка

Включите отладку в конфигурации:

```env
NUXT_PUBLIC_DEBUG=true
```

Это выведет в консоль:
- Статус инициализации GDPR
- Отправляемые события
- Ошибки загрузки GTM
- UTM данные

## Тестирование

Для тестирования используйте компонент `AnalyticsExample`:

```vue
<template>
  <AnalyticsAnalyticsExample />
</template>
```

Этот компонент предоставляет интерфейс для:
- Управления GDPR настройками
- Просмотра UTM данных
- Тестирования аналитических событий
- Проверки состояния системы

## Требования к базе данных

Для полной функциональности необходимо создать таблицу `utm_visits`:

```sql
CREATE TABLE utm_visits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ga VARCHAR(255),              -- Google Analytics Client ID
    did VARCHAR(255),             -- Device ID
    utm_source VARCHAR(255),      -- Источник трафика
    utm_medium VARCHAR(255),      -- Канал трафика
    utm_campaign VARCHAR(255),    -- Название кампании
    utm_content VARCHAR(255),     -- Содержание объявления
    utm_term VARCHAR(255),        -- Ключевое слово
    referer VARCHAR(2048),        -- HTTP Referer
    url VARCHAR(2048),            -- Текущий URL
    ip VARCHAR(255),              -- IP адрес
    ua VARCHAR(255),              -- User Agent
    created_at INT NOT NULL       -- Время создания (timestamp)
);
```
