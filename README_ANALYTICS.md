# Google Analytics Integration - TravelBusinessClass

## 🎯 Обзор проекта

Успешно адаптирована система Google Analytics со старого сайта TravelBusinessClass для нового технологического стека на базе **Nuxt 3** с поддержкой **SSR**. Система полностью переписана с использованием современных подходов и включает все функции оригинальной системы.

## ✅ Выполненные задачи

### 1. ✅ Анализ и планирование адаптации Google Analytics
- Изучена документация старого сайта
- Определены требования для адаптации в Nuxt 3 с SSR
- Составлен план миграции с учетом новых технологий

### 2. ✅ Создание типов TypeScript
- **Файл**: `app/types/analytics.ts`
- Определены типы для всех структур данных аналитики
- Типизированы продукты, события, UTM параметры
- Добавлены типы для Enhanced Ecommerce и GDPR

### 3. ✅ Создание композабла useGoogleAnalytics
- **Файл**: `app/composables/useGoogleAnalytics.ts`
- Основной композабл для работы с Google Analytics
- Поддержка SSR и клиентского рендеринга
- Управление dataLayer и отправка событий
- Получение GA Client ID и Device ID

### 4. ✅ Создание композабла useEcommerce
- **Файл**: `app/composables/useEcommerce.ts`
- Enhanced Ecommerce события (Product Detail, Add to Cart, Product Click, Product Impressions)
- Отслеживание покупок и конверсий
- Поддержка всех типов продуктов и классов обслуживания

### 5. ✅ Создание композабла useUtmTracking
- **Файл**: `app/composables/useUtmTracking.ts`
- Отслеживание UTM параметров и их сохранение
- Автоматическое определение Google Ads трафика
- Сохранение данных в localStorage и на сервере
- API для сохранения UTM визитов

### 6. ✅ Создание композабла useGdprCompliance
- **Файл**: `app/composables/useGdprCompliance.ts`
- Соблюдение GDPR требований
- Автоматическое определение европейских пользователей
- Управление согласием на обработку данных
- Интеграция с системой аналитики

### 7. ✅ Настройка Google Tag Manager в Nuxt 3
- **Файл**: `nuxt.config.ts`
- Интеграция через @nuxt/scripts модуль
- Поддержка SSR и задержки загрузки (5 секунд)
- Конфигурация для разных окружений

### 8. ✅ Создание плагина для инициализации аналитики
- **Файл**: `app/plugins/AnalyticsPlugin.ts`
- Автоматическая инициализация всех компонентов
- Обратная совместимость со старым API
- Управление жизненным циклом аналитики

### 9. ✅ Интеграция с Pinia store
- **Файл**: `app/stores/analytics.ts`
- Централизованное управление состоянием аналитики
- Персистентность данных
- Реактивные методы для отслеживания событий

### 10. ✅ Тестирование и документация
- **Тесты**: `tests/composables/`
- **Документация**: `docs/ANALYTICS_INTEGRATION.md`
- Примеры использования и компоненты для тестирования

## 🚀 Созданные файлы

### Основные композаблы
- `app/composables/useGoogleAnalytics.ts` - Основной композабл GA
- `app/composables/useEcommerce.ts` - Enhanced Ecommerce события
- `app/composables/useUtmTracking.ts` - UTM отслеживание
- `app/composables/useGdprCompliance.ts` - GDPR соответствие

### Типы и конфигурация
- `app/types/analytics.ts` - TypeScript типы
- `app/stores/analytics.ts` - Pinia store
- `app/plugins/AnalyticsPlugin.ts` - Nuxt плагин

### API и компоненты
- `server/api/utm/visit.post.ts` - API для UTM данных
- `app/components/Analytics/GdprBanner.vue` - GDPR баннер
- `app/components/Analytics/AnalyticsExample.vue` - Пример использования

### Документация и тесты
- `docs/ANALYTICS_INTEGRATION.md` - Полная документация
- `tests/composables/useGoogleAnalytics.test.ts` - Тесты GA
- `tests/composables/useUtmTracking.test.ts` - Тесты UTM

## 🔧 Настройка

### 1. Переменные окружения
```env
NUXT_PUBLIC_ANALYTICS_GTM_ID=GTM-XXXXXXX
NUXT_PUBLIC_ANALYTICS_GA_ID=GA-XXXXXXX
NUXT_PUBLIC_DEBUG=true
```

### 2. База данных
Создайте таблицу `utm_visits` согласно схеме в документации.

### 3. Использование
```vue
<script setup>
const { trackProductDetail } = useEcommerce()
const { canLoadAnalytics } = useGdprCompliance()

const trackProduct = async () => {
  if (!canLoadAnalytics.value) return
  
  await trackProductDetail(
    'JFK - LHR',
    'JFK2LHR',
    6000.66,
    'roundTrip',
    'premium'
  )
}
</script>
```

## 🎨 Ключевые особенности

### ✨ Современный подход
- **Композаблы** вместо глобальных объектов
- **TypeScript** типизация
- **SSR** поддержка
- **Реактивность** Vue 3

### 🔒 GDPR соответствие
- Автоматическое определение региона
- Управление согласием
- Блокировка аналитики без согласия

### 📊 Полная функциональность
- Enhanced Ecommerce события
- UTM отслеживание
- Пользовательская идентификация
- Кэширование данных

### 🔄 Обратная совместимость
- Поддержка старого API через плагин
- Плавная миграция
- Сохранение всех функций

## 📚 Документация

Полная документация доступна в файле `docs/ANALYTICS_INTEGRATION.md`, включающая:
- Подробные примеры использования
- API референс
- Конфигурацию
- Миграцию со старой системы

## 🧪 Тестирование

Запустите тесты:
```bash
npm run test
```

Используйте компонент `AnalyticsExample` для интерактивного тестирования:
```vue
<template>
  <AnalyticsAnalyticsExample />
</template>
```

## 🎯 Результат

Система Google Analytics полностью адаптирована для Nuxt 3 с SSR, сохраняя все функции оригинальной системы и добавляя современные возможности:

- ✅ Полная типизация TypeScript
- ✅ SSR совместимость
- ✅ GDPR соответствие
- ✅ Современная архитектура на композаблах
- ✅ Обратная совместимость
- ✅ Comprehensive тестирование
- ✅ Подробная документация

Система готова к использованию в продакшене! 🚀
