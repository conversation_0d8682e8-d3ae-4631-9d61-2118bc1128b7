import { BestDealClass, type BestDeals } from '~/stores/useBestDealsStore'
import type { ApiResponseType } from '~~/server/api/best-deals.get'

export default defineEventHandler(async (): Promise<ApiResponseType<Partial<BestDeals>[]>> => {
    return {
        items: [
            {
                route: '/',
                imageSrc: 'https://travelbusinessclass.com/assets/image/offer_page_1/5b247/region-Europe__fix_364_307.jpg',
                class: BestDealClass.Business,
                destination: 'Europe',
            },
            {
                route: '/',
                imageSrc: 'https://travelbusinessclass.com/assets/image/offer_page_3/1a0a8/region-Oceania__fix_364_307.jpg',
                class: BestDealClass.Business,
                destination: 'Oceania',
            },
            {
                route: '/',
                imageSrc: 'https://travelbusinessclass.com/assets/image/offer_page_4/63af7/region-Asia__fix_364_307.jpg',
                class: BestDealClass.Business,
                destination: 'Asia',
            },
            {
                route: '/',
                imageSrc: 'https://travelbusinessclass.com/assets/image/offer_page_5/ea624/region-Africa__fix_364_307.jpg',
                class: BestDealClass.Business,
                destination: 'Africa',
            },
            {
                route: '/',
                imageSrc: 'https://travelbusinessclass.com/assets/image/offer_page_6/8bb8d/region-Middle+East__fix_364_307.jpg',
                class: BestDealClass.Business,
                destination: 'Middle East',
            },
            {
                route: '/',
                imageSrc: 'https://travelbusinessclass.com/assets/image/offer_page_7/f20c8/region-Indian+Subc.__fix_364_307.jpg',
                class: BestDealClass.Business,
                destination: 'Indian Subc.',
            },
        ],
    }
})
