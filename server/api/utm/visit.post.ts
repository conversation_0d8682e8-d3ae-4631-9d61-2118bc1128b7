import type { UtmVisit } from '~/types/analytics'

/**
 * API endpoint for saving UTM visit data
 * This endpoint would need to be connected to your database
 */
export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event) as UtmVisit
        
        // Get client IP and User Agent from headers
        const clientIP = getClientIP(event)
        const userAgent = getHeader(event, 'user-agent')
        const referer = getHeader(event, 'referer')
        
        // Prepare visit data
        const visitData: UtmVisit = {
            ...body,
            ip: clientIP,
            ua: userAgent,
            referer: referer,
            created_at: Math.floor(Date.now() / 1000),
        }

        // Validate required fields
        if (!visitData.ga && !visitData.did) {
            throw createError({
                statusCode: 400,
                statusMessage: 'Either Google Analytics Client ID or Device ID is required',
            })
        }

        // Here you would save to your database
        // Example with a hypothetical database service:
        /*
        const db = useDatabase()
        const result = await db.collection('utm_visits').insert(visitData)
        
        return {
            success: true,
            id: result.insertedId,
        }
        */

        // For now, just log the data (remove in production)
        console.log('[UTM API] Visit data received:', visitData)

        // Return success response
        return {
            success: true,
            message: 'UTM visit data saved successfully',
            data: {
                id: Date.now(), // Mock ID
                created_at: visitData.created_at,
            },
        }

    } catch (error) {
        console.error('[UTM API] Error saving visit:', error)
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to save UTM visit data',
        })
    }
})
