import { type BestDealsListType, BestDealType } from '~/stores/useBestDealsListStore'
import type { ApiResponseType } from '~~/server/api/best-deals.get'

export default defineEventHandler(async (): Promise<ApiResponseType<BestDealsListType[]>> => {
    return {
        items: [
            {
                group: BestDealType.Region,
                values: [
                    {
                        title: 'Europe',
                        url: '/',
                    },
                    {
                        title: 'Oceania',
                        url: '/',
                    },
                    {
                        title: 'Asia',
                        url: '/',
                    },
                    {
                        title: 'Africa',
                        url: '/',
                    },
                    {
                        title: 'Middle East',
                        url: '/',
                    },
                    {
                        title: 'Indian Subc.',
                        url: '/',
                    },
                    {
                        title: 'Latin America',
                        url: '/',
                    },
                ],
            },
            {
                group: BestDealType.Country,
                values: [
                    {
                        title: 'Croatia',
                        url: '/',
                    },
                    {
                        title: 'Autria',
                        url: '/',
                    },
                    {
                        title: 'France',
                        url: '/',
                    },
                    {
                        title: 'Germany',
                        url: '/',
                    },
                    {
                        title: 'Greece',
                        url: '/',
                    },
                    {
                        title: 'Iceland',
                        url: '/',
                    },
                    {
                        title: 'Italy',
                        url: '/',
                    },
                ],
            },
            {
                group: BestDealType.City,
                values: [
                    {
                        title: 'Sydney',
                        url: '/',
                    },
                    {
                        title: 'Papeete',
                        url: '/',
                    },
                    {
                        title: 'Male',
                        url: '/',
                    },
                    {
                        title: 'Tel Aviv',
                        url: '/',
                    },
                    {
                        title: 'Istambul',
                        url: '/',
                    },
                    {
                        title: 'Dubai',
                        url: '/',
                    },
                    {
                        title: 'Nairobi',
                        url: '/',
                    },
                ],
            },
            {
                group: BestDealType.Airline,
                values: [
                    {
                        title: 'Delta',
                        url: '/',
                    },
                    {
                        title: 'Emirates',
                        url: '/',
                    },
                    {
                        title: 'United',
                        url: '/',
                    },
                    {
                        title: 'Air France',
                        url: '/',
                    },
                    {
                        title: 'Turkish',
                        url: '/',
                    },
                    {
                        title: 'Lufthansa',
                        url: '/',
                    },
                ],
            },
        ],
    }
})
