# Multi-stage build for Nuxt 3 SSR application

# Stage 1: Build stage
FROM node:22-alpine AS builder

# Set working directory
WORKDIR /app

# Install minimal system dependencies
# Uncomment the following line if you encounter native module compilation errors:
# RUN apk add --no-cache git python3 make g++

# Copy package files
COPY package.json yarn.lock ./

# Install dependencies
# If build fails with native module errors, uncomment the system dependencies above
RUN yarn install --frozen-lockfile --production=false

# Copy source code
COPY . .

# Build the application for production
RUN yarn build:prod

# Stage 2: Production stage
FROM node:22-alpine AS production

# Set working directory
WORKDIR /app

# Install system dependencies for production
RUN apk add --no-cache \
    dumb-init \
    wget

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nuxt -u 1001

# Copy package files
COPY package.json yarn.lock ./

# Install only production dependencies
RUN yarn install --frozen-lockfile --production=true && \
    yarn cache clean

# Copy built application from builder stage
COPY --from=builder --chown=nuxt:nodejs /app/.output-full ./.output-full

# Copy public assets
COPY --chown=nuxt:nodejs public ./public

# Set environment variables
ENV NODE_ENV=production
ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=3000

# Switch to non-root user
USER nuxt

# Expose port
EXPOSE 3000

# Health check (simple HTTP check)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1

# Start the application with dumb-init
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", ".output-full/server/index.mjs"]
