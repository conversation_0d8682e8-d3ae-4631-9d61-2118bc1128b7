#image: php:zts-alpine3.20
image: atlassian/default-image:4
pipelines:
  custom:
    create_build_from_BRANCH_only:
      - step:
          name: 'Build & Push Image'
          services:
            - docker
          caches:
            - docker
          script:
            - echo $BITBUCKET_BRANCH
            - if [ -z ${BITBUCKET_BRANCH+x} ]; then exit 1; fi
            - echo "STEP_APP_IMAGE_NAME=registry.digitalocean.com/$TMG_REGISTRY_NAME/$BITBUCKET_REPO_SLUG" >> step_env_vars.txt
            - echo "STEP_IMAGE_TAG=build-$BITBUCKET_BUILD_NUMBER" >> step_env_vars.txt
            - echo "STEP_IMAGE_BUILD=registry.digitalocean.com\\\\/$TMG_REGISTRY_NAME\\\\/$BITBUCKET_REPO_SLUG\\\\:build-$BITBUCKET_BUILD_NUMBER" >> step_env_vars.txt
            - source step_env_vars.txt
            - echo  "$STEP_IMAGE_BUILD"
            - sed -i "s/^{/{\n\t\"buildImage\":\"$STEP_IMAGE_BUILD\",/" ./kubernetes/kubernetes.json
            - cat ./kubernetes/kubernetes.json
            - echo $TMG_REGISTRY_TOKEN | docker login registry.digitalocean.com --username $TMG_REGISTRY_TOKEN --password-stdin
            - export DOCKER_BUILDKIT=1
            - docker build --file Dockerfile -t $STEP_APP_IMAGE_NAME:$STEP_IMAGE_TAG .
            - docker push $STEP_APP_IMAGE_NAME:$STEP_IMAGE_TAG
            - git tag build-${BITBUCKET_BUILD_NUMBER} ${BITBUCKET_COMMIT}
            - git push origin --tags
          artifacts:
            - kubernetes/*
            - step_env_vars.txt

      - step:
          name: Publish Build
          services:
            - docker
          caches:
            - docker
          clone:
            enabled: false
          script:
            - pipe: atlassian/aws-s3-deploy:1.6.1
              variables:
                AWS_ACCESS_KEY_ID: $BUILD_AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $BUILD_AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: 'fra1'
                S3_BUCKET: "tmgbuild/builds/${BITBUCKET_REPO_SLUG}/${BITBUCKET_BRANCH}"
                LOCAL_PATH: 'kubernetes'
                EXTRA_ARGS: '--endpoint=https://fra1.digitaloceanspaces.com/ --acl=public-read'
                CACHE_CONTROL: 'no-cache'

definitions:
  services:
    docker:
      memory: 2048
