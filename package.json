{"name": "tbc-client-site-frontend", "private": true, "type": "module", "scripts": {"clear-output": "rm -rf ./.output-full", "build:prod": "yarn build --dotenv .env.production", "build:dev": "yarn build --dotenv .env.development", "build": "nuxt build", "dev": "nuxt dev --dotenv .env.development", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint", "fix": "eslint --fix", "analyze": "SSR=false nuxi analyze", "check-types": "nuxi typecheck", "test": "vitest"}, "dependencies": {"@nuxt/image": "^1.10.0", "@nuxt/scripts": "0.11.5", "@nuxtjs/html-validator": "^1.6.0", "@pinia/nuxt": "^0.11.0", "@popperjs/core": "^2.11.8", "@tmg/aero-data-sdk": "^0.1.3", "@tmg/flight-form": "^0.1.6", "@tmg/form": "^0.1.5", "@tmg/modals": "^1.3.5", "@types/node": "^18.19", "@unhead/vue": "^2.0.10", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "^13.2.0", "consola": "^3.4.2", "dayjs": "^1.11.13", "libphonenumber-js": "^1.12.8", "maska": "^2.1.11", "nuxt": "^3.17.4", "nuxt-svgo": "^4.2.1", "pinia": "^3.0.2", "swiper": "^11.0.5", "zod": "^4.0.0-beta.20250505T195954"}, "devDependencies": {"@nuxt/test-utils": "^3.19.0", "@nuxtjs/tailwindcss": "6.13.2", "@tailwindcss/vite": "^4.1.7", "@tmg/lint": "^2.0", "@total-typescript/ts-reset": "^0.6.1", "ofetch": "^1.4.1", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "vitest": "^3.1.4", "vue": "^3.5.14"}, "browserslist": [">0.1%", "not dead"], "gitHooks": {"pre-commit": ["yarn lint-staged --allow-empty"]}, "lint-staged": {"*.{js,ts,mjs,jsx,vue}": ["yarn fix", "git add"]}, "packageManager": "yarn@1.22.19", "engines": {"node": "^22.0"}}